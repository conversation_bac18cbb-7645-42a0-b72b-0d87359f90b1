import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { systemLogDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { fileUploadRateLimit } from '@/lib/rateLimiter';
import {
  withSecureErrorHandling,
  createValidationError,
  createRateLimitError,
  createSecureResponse
} from '@/lib/secureErrorHandler';

// Magic bytes for image file validation
function checkImageMagicBytes(buffer: Buffer): boolean {
  if (buffer.length < 4) return false;

  // JPEG: FF D8 FF
  if (buffer[0] === 0xFF && buffer[1] === 0xD8 && buffer[2] === 0xFF) return true;

  // PNG: 89 50 4E 47
  if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) return true;

  // WebP: 52 49 46 46 (RIFF) + WebP signature
  if (buffer.length >= 12 &&
      buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46 &&
      buffer[8] === 0x57 && buffer[9] === 0x45 && buffer[10] === 0x42 && buffer[11] === 0x50) return true;

  return false;
}

// Secure KYC document upload handler
const kycUploadHandler = async (request: NextRequest) => {
  // Check rate limiting first
  const rateLimitResult = fileUploadRateLimit(request);
  if (!rateLimitResult.allowed) {
    const error = createRateLimitError(
      rateLimitResult.retryAfter,
      rateLimitResult.remaining,
      rateLimitResult.resetTime
    );
    return createSecureResponse(error);
  }

  const { authenticated, user } = await authenticateRequest(request);

  if (!authenticated || !user) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }

  const formData = await request.formData();
  const file = formData.get('file') as File;
  const documentType = formData.get('documentType') as string;
  const idType = formData.get('idType') as string;
  const documentSide = formData.get('documentSide') as string;

  // Validate file presence
  if (!file) {
    const error = createValidationError('No file provided');
    return createSecureResponse(error);
  }

  // Basic form data validation
  if (!documentType || !['ID_DOCUMENT', 'SELFIE'].includes(documentType)) {
    const error = createValidationError('Invalid document type');
    return createSecureResponse(error);
  }

  if (documentType === 'ID_DOCUMENT') {
    if (!idType || !['NATIONAL_ID', 'PASSPORT', 'DRIVING_LICENSE'].includes(idType)) {
      const error = createValidationError('Invalid ID type');
      return createSecureResponse(error);
    }
    if (!documentSide || !['FRONT', 'BACK'].includes(documentSide)) {
      const error = createValidationError('Invalid document side');
      return createSecureResponse(error);
    }
  }

  const validatedData = {
    documentType,
    idType: documentType === 'ID_DOCUMENT' ? idType : undefined,
    documentSide: documentType === 'ID_DOCUMENT' ? documentSide : undefined,
  };

  // Enhanced file validation
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  // Basic file validation
  if (!allowedTypes.includes(file.type)) {
    const error = createValidationError(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
    return createSecureResponse(error);
  }

  if (file.size > maxSize) {
    const error = createValidationError(`File size ${Math.round(file.size / 1024 / 1024)}MB exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`);
    return createSecureResponse(error);
  }

  // Additional security checks
  if (file.size < 1024) { // File too small (likely not a real image)
    const error = createValidationError('File size too small. Please upload a valid image.');
    return createSecureResponse(error);
  }

  // Create secure upload directory if it doesn't exist
  const uploadDir = join(process.cwd(), 'public', 'uploads', 'kyc');
  try {
    await mkdir(uploadDir, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }

  // Generate secure filename with validation
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  if (!fileExtension || !['jpg', 'jpeg', 'png', 'webp'].includes(fileExtension)) {
    const error = createValidationError('Invalid file extension. Only JPG, PNG, and WebP files are allowed.');
    return createSecureResponse(error);
  }

  // Create secure filename structure
  const timestamp = Date.now();
  const randomId = uuidv4().substring(0, 8);
  const fileNameParts = [user.id, documentType, timestamp, randomId];

  if (validationResult.data.idType) fileNameParts.push(validationResult.data.idType);
  if (validationResult.data.documentSide) fileNameParts.push(validationResult.data.documentSide);

  const fileName = `${fileNameParts.join('_')}.${fileExtension}`;
  const filePath = join(uploadDir, fileName);
  const publicPath = `/uploads/kyc/${fileName}`;

  // Additional security: Check file header (magic bytes) to verify it's actually an image
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);

  // Check magic bytes for common image formats
  const isValidImage = checkImageMagicBytes(buffer);
  if (!isValidImage) {
    const error = createValidationError('Invalid image file. File may be corrupted or not a valid image.');
    return createSecureResponse(error);
  }

  // Save file securely
  await writeFile(filePath, buffer);

  // Check if document already exists and update or create
  const whereClause: any = {
    userId: user.id,
    documentType: validationResult.data.documentType as 'ID_DOCUMENT' | 'SELFIE',
  };

  // For ID documents, also match idType and documentSide
  if (validationResult.data.documentType === 'ID_DOCUMENT') {
    whereClause.idType = validationResult.data.idType as 'NATIONAL_ID' | 'PASSPORT' | 'DRIVING_LICENSE';
    whereClause.documentSide = validationResult.data.documentSide as 'FRONT' | 'BACK';
  }

  const existingDocument = await prisma.kYCDocument.findFirst({
    where: whereClause,
  });

  let kycDocument;

  if (existingDocument) {
    // Update existing document
    kycDocument = await prisma.kYCDocument.update({
      where: { id: existingDocument.id },
      data: {
        filePath: publicPath,
        status: 'PENDING',
        reviewedAt: null,
        reviewedBy: null,
        rejectionReason: null,
        updatedAt: new Date(),
      },
    });
  } else {
    // Create new document
    const createData: any = {
      userId: user.id,
      documentType: validationResult.data.documentType as 'ID_DOCUMENT' | 'SELFIE',
      filePath: publicPath,
      status: 'PENDING',
    };

    // Add idType and documentSide for ID documents
    if (validationResult.data.documentType === 'ID_DOCUMENT') {
      createData.idType = validationResult.data.idType as 'NATIONAL_ID' | 'PASSPORT' | 'DRIVING_LICENSE';
      createData.documentSide = validationResult.data.documentSide as 'FRONT' | 'BACK';
    }

    kycDocument = await prisma.kYCDocument.create({
      data: createData,
    });
  }

  // Check if user has uploaded all required documents and update KYC status
  const userDocuments = await prisma.kYCDocument.findMany({
    where: { userId: user.id },
  });

  const hasSelfieDocument = userDocuments.some(doc => doc.documentType === 'SELFIE');

  // Check if user has complete ID documents based on their ID type
  let hasCompleteIdDocuments = false;
  const userIdType = userDocuments.find(doc => doc.documentType === 'ID_DOCUMENT')?.idType;

  if (userIdType) {
    const idDocuments = userDocuments.filter(doc =>
      doc.documentType === 'ID_DOCUMENT' && doc.idType === userIdType
    );

    if (userIdType === 'PASSPORT') {
      // Passport only needs front side
      hasCompleteIdDocuments = idDocuments.some(doc => doc.documentSide === 'FRONT');
    } else {
      // National ID and Driving License need both front and back
      const hasFront = idDocuments.some(doc => doc.documentSide === 'FRONT');
      const hasBack = idDocuments.some(doc => doc.documentSide === 'BACK');
      hasCompleteIdDocuments = hasFront && hasBack;
    }
  }

  if (hasCompleteIdDocuments && hasSelfieDocument) {
    // Update user KYC status to PENDING if all required documents are uploaded
    await prisma.user.update({
      where: { id: user.id },
      data: { kycStatus: 'PENDING' },
    });
  }

  // Log the secure upload
  await systemLogDb.create({
    action: 'KYC_DOCUMENT_UPLOADED',
    userId: user.id,
    details: {
      documentType: validationResult.data.documentType,
      idType: validationResult.data.idType || null,
      documentSide: validationResult.data.documentSide || null,
      fileName,
      fileSize: file.size,
      documentId: kycDocument.id,
      uploadTime: new Date().toISOString(),
    },
    ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
  });

  const response = NextResponse.json({
    success: true,
    message: 'Document uploaded successfully',
    data: {
      documentId: kycDocument.id,
      documentType: kycDocument.documentType,
      status: kycDocument.status,
    },
  });

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
};

// Export the handler with secure error handling
export const POST = withSecureErrorHandling(kycUploadHandler, {
  endpoint: '/api/kyc/upload',
  requireAuth: true,
});
