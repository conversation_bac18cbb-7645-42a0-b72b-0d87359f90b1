/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/email-templates/seed/route";
exports.ids = ["app/api/admin/email-templates/seed/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_admin_email_templates_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/email-templates/seed/route.ts */ \"(rsc)/./src/app/api/admin/email-templates/seed/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/email-templates/seed/route\",\n        pathname: \"/api/admin/email-templates/seed\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/email-templates/seed/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\admin\\\\email-templates\\\\seed\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_admin_email_templates_seed_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/email-templates/seed/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/admin/email-templates/seed/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_errorLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/errorLogger */ \"(rsc)/./src/lib/errorLogger.ts\");\n\n\n\n\nconst defaultTemplates = [\n    {\n        name: 'otp_verification',\n        subject: 'Email Verification - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Email Verification</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Sustainable Mining Platform</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>Thank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:</p>\n\n        <div style=\"background: #fff; border: 2px dashed #667eea; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;\">\n            <h3 style=\"margin: 0; color: #667eea; font-size: 32px; letter-spacing: 5px;\">{{otp}}</h3>\n            <p style=\"margin: 10px 0 0 0; color: #666; font-size: 14px;\">This code expires in 10 minutes</p>\n        </div>\n\n        <p>If you didn't create an account with HashCoreX, please ignore this email.</p>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br>The HashCoreX Team</p>\n            <p style=\"margin-top: 20px;\">\n                <a href=\"#\" style=\"color: #667eea; text-decoration: none;\">Visit HashCoreX</a> |\n                <a href=\"#\" style=\"color: #667eea; text-decoration: none;\">Support</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nThank you for registering with HashCoreX. To complete your registration, please verify your email address using the OTP code below:\n\nYour OTP Code: {{otp}}\n\nThis code expires in 10 minutes.\n\nIf you didn't create an account with HashCoreX, please ignore this email.\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'welcome_email',\n        subject: 'Welcome to HashCoreX - Your Mining Journey Begins!',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Welcome to HashCoreX</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">Welcome to HashCoreX!</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Your Sustainable Mining Journey Starts Here</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>Congratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;\">\n            <h3 style=\"margin-top: 0; color: #667eea;\">What's Next?</h3>\n            <ul style=\"margin: 0; padding-left: 20px;\">\n                <li>Complete your KYC verification</li>\n                <li>Explore our mining packages</li>\n                <li>Start earning with sustainable mining</li>\n                <li>Refer friends and earn bonuses</li>\n            </ul>\n        </div>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"#\" style=\"background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">Access Dashboard</a>\n        </div>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Need help? Contact our support team anytime.</p>\n            <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nCongratulations! Your HashCoreX account has been successfully created. You're now part of our sustainable mining community.\n\nWhat's Next?\n- Complete your KYC verification\n- Explore our mining packages\n- Start earning with sustainable mining\n- Refer friends and earn bonuses\n\nVisit your dashboard to get started: [Dashboard Link]\n\nNeed help? Contact our support team anytime.\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'deposit_success',\n        subject: 'Deposit Confirmed - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Deposit Confirmed</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Deposit Confirmed!</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>Great news! Your deposit has been successfully confirmed and credited to your account.</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin-top: 0; color: #10b981;\">Deposit Details</h3>\n            <p><strong>Amount:</strong> {{amount}} {{currency}}</p>\n            <p><strong>Transaction ID:</strong> {{transactionId}}</p>\n            <p><strong>Status:</strong> <span style=\"color: #10b981;\">Confirmed</span></p>\n        </div>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"#\" style=\"background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">View Dashboard</a>\n        </div>\n\n        <p>You can now use these funds to purchase mining packages and start earning!</p>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br>The HashCoreX Team</p>\n            <p style=\"margin-top: 20px;\">\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Visit HashCoreX</a> |\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Support</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nGreat news! Your deposit has been successfully confirmed and credited to your account.\n\nDeposit Details:\n- Amount: {{amount}} {{currency}}\n- Transaction ID: {{transactionId}}\n- Status: Confirmed\n\nYou can now use these funds to purchase mining packages and start earning!\n\nVisit your dashboard to get started: [Dashboard Link]\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'kyc_approved',\n        subject: 'KYC Verification Approved - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>KYC Approved</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">KYC Verification Approved!</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>Congratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin-top: 0; color: #10b981;\">What's Now Available?</h3>\n            <ul style=\"margin: 0; padding-left: 20px;\">\n                <li>Full access to mining packages</li>\n                <li>Unlimited deposits and withdrawals</li>\n                <li>Access to premium features</li>\n                <li>Higher referral commissions</li>\n            </ul>\n        </div>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"#\" style=\"background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">Access Dashboard</a>\n        </div>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br>The HashCoreX Team</p>\n            <p style=\"margin-top: 20px;\">\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Visit HashCoreX</a> |\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Support</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nCongratulations! Your KYC verification has been approved. Your account is now fully verified and you have access to all HashCoreX features.\n\nWhat's Now Available?\n- Full access to mining packages\n- Unlimited deposits and withdrawals\n- Access to premium features\n- Higher referral commissions\n\nVisit your dashboard to get started: [Dashboard Link]\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'kyc_rejected',\n        subject: 'KYC Verification Update - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>KYC Update</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">KYC Verification Update</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>We have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;\">\n            <h3 style=\"margin-top: 0; color: #ef4444;\">Reason for Review</h3>\n            <p>{{rejectionReason}}</p>\n        </div>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;\">\n            <h3 style=\"margin-top: 0; color: #667eea;\">Next Steps</h3>\n            <ul style=\"margin: 0; padding-left: 20px;\">\n                <li>Review the feedback above</li>\n                <li>Prepare updated documents</li>\n                <li>Resubmit your KYC application</li>\n                <li>Contact support if you need help</li>\n            </ul>\n        </div>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"#\" style=\"background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">Resubmit KYC</a>\n        </div>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br>The HashCoreX Team</p>\n            <p style=\"margin-top: 20px;\">\n                <a href=\"#\" style=\"color: #667eea; text-decoration: none;\">Visit HashCoreX</a> |\n                <a href=\"#\" style=\"color: #667eea; text-decoration: none;\">Support</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nWe have reviewed your KYC verification documents. Unfortunately, we need additional information to complete your verification.\n\nReason for Review:\n{{rejectionReason}}\n\nNext Steps:\n- Review the feedback above\n- Prepare updated documents\n- Resubmit your KYC application\n- Contact support if you need help\n\nVisit your dashboard to resubmit: [Dashboard Link]\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'withdrawal_approved',\n        subject: 'Withdrawal Approved - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Withdrawal Approved</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Withdrawal Approved!</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>Good news! Your withdrawal request has been approved and is being processed.</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin-top: 0; color: #10b981;\">Withdrawal Details</h3>\n            <p><strong>Amount:</strong> {{amount}} USDT</p>\n            <p><strong>Destination:</strong> {{usdtAddress}}</p>\n            <p><strong>Status:</strong> <span style=\"color: #10b981;\">Approved - Processing</span></p>\n        </div>\n\n        <p>Your funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.</p>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"#\" style=\"background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">View Transaction</a>\n        </div>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br>The HashCoreX Team</p>\n            <p style=\"margin-top: 20px;\">\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Visit HashCoreX</a> |\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Support</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nGood news! Your withdrawal request has been approved and is being processed.\n\nWithdrawal Details:\n- Amount: {{amount}} USDT\n- Destination: {{usdtAddress}}\n- Status: Approved - Processing\n\nYour funds will be transferred to your wallet within 24 hours. You will receive another email once the transaction is completed.\n\nVisit your dashboard: [Dashboard Link]\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'withdrawal_completed',\n        subject: 'Withdrawal Completed - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Withdrawal Completed</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Withdrawal Completed!</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>Your withdrawal has been successfully completed! The funds have been transferred to your wallet.</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin-top: 0; color: #10b981;\">Transaction Details</h3>\n            <p><strong>Amount:</strong> {{amount}} USDT</p>\n            <p><strong>Destination:</strong> {{usdtAddress}}</p>\n            <p><strong>Transaction Hash:</strong> {{transactionHash}}</p>\n            <p><strong>Status:</strong> <span style=\"color: #10b981;\">Completed</span></p>\n        </div>\n\n        <p>You can verify this transaction on the blockchain using the transaction hash above.</p>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"#\" style=\"background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">View on Blockchain</a>\n        </div>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br>The HashCoreX Team</p>\n            <p style=\"margin-top: 20px;\">\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Visit HashCoreX</a> |\n                <a href=\"#\" style=\"color: #10b981; text-decoration: none;\">Support</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nYour withdrawal has been successfully completed! The funds have been transferred to your wallet.\n\nTransaction Details:\n- Amount: {{amount}} USDT\n- Destination: {{usdtAddress}}\n- Transaction Hash: {{transactionHash}}\n- Status: Completed\n\nYou can verify this transaction on the blockchain using the transaction hash above.\n\nVisit your dashboard: [Dashboard Link]\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'withdrawal_rejected',\n        subject: 'Withdrawal Update - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Withdrawal Update</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Withdrawal Update</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>We regret to inform you that your withdrawal request could not be processed at this time.</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #ef4444;\">\n            <h3 style=\"margin-top: 0; color: #ef4444;\">Withdrawal Details</h3>\n            <p><strong>Amount:</strong> {{amount}} USDT</p>\n            <p><strong>Reason:</strong> {{rejectionReason}}</p>\n        </div>\n\n        <p>The requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.</p>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"#\" style=\"background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">Contact Support</a>\n        </div>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br>The HashCoreX Team</p>\n            <p style=\"margin-top: 20px;\">\n                <a href=\"#\" style=\"color: #667eea; text-decoration: none;\">Visit HashCoreX</a> |\n                <a href=\"#\" style=\"color: #667eea; text-decoration: none;\">Support</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Hello {{firstName}}!\n\nWe regret to inform you that your withdrawal request could not be processed at this time.\n\nWithdrawal Details:\n- Amount: {{amount}} USDT\n- Reason: {{rejectionReason}}\n\nThe requested amount has been returned to your account balance. Please review the reason above and contact our support team if you need assistance.\n\nVisit your dashboard: [Dashboard Link]\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'test_email',\n        subject: 'HashCoreX Email Test',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Email Test</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Email Configuration Test</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Email Test Successful!</h2>\n\n        <p>This is a test email to verify your SMTP configuration.</p>\n        <p>If you received this email, your email settings are working correctly!</p>\n\n        <div style=\"background: #fff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #667eea;\">\n            <h3 style=\"margin-top: 0; color: #667eea;\">Test Details</h3>\n            <p><strong>Test Date:</strong> {{testDate}}</p>\n            <p><strong>Status:</strong> <span style=\"color: #10b981;\">Success</span></p>\n        </div>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Your email configuration is working properly.</p>\n            <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `HashCoreX Email Test\n\nThis is a test email to verify your SMTP configuration.\nIf you received this email, your email settings are working correctly!\n\nTest Date: {{testDate}}\nStatus: Success\n\nYour email configuration is working properly.\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'password_reset_otp',\n        subject: 'Password Reset - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Password Reset</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Password Reset Request</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>We received a request to reset your password for your HashCoreX account. If you made this request, please use the verification code below:</p>\n\n        <div style=\"background: #fff; padding: 30px; margin: 30px 0; border-radius: 10px; text-align: center; border: 2px solid #ef4444;\">\n            <h2 style=\"color: #ef4444; margin: 0 0 10px 0; font-size: 36px; letter-spacing: 8px; font-weight: bold;\">{{otp}}</h2>\n            <p style=\"color: #666; margin: 0; font-size: 14px;\">This code will expire in 10 minutes</p>\n        </div>\n\n        <div style=\"background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;\">\n            <p style=\"margin: 0; color: #856404; font-size: 14px;\">\n                <strong>Security Notice:</strong> If you didn't request this password reset, please ignore this email. Your account remains secure.\n            </p>\n        </div>\n\n        <p>For your security:</p>\n        <ul style=\"color: #666; font-size: 14px;\">\n            <li>This code is valid for 10 minutes only</li>\n            <li>Don't share this code with anyone</li>\n            <li>If you didn't request this reset, your account is still secure</li>\n        </ul>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Need help? Contact our support team.</p>\n            <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `HashCoreX Password Reset\n\nHello {{firstName}}!\n\nWe received a request to reset your password for your HashCoreX account. If you made this request, please use the verification code below:\n\nYour verification code: {{otp}}\n\nThis code will expire in 10 minutes.\n\nSecurity Notice: If you didn't request this password reset, please ignore this email. Your account remains secure.\n\nFor your security:\n- This code is valid for 10 minutes only\n- Don't share this code with anyone\n- If you didn't request this reset, your account is still secure\n\nNeed help? Contact our support team.\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'mining_unit_purchase',\n        subject: 'Mining Unit Purchase Confirmed - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Mining Unit Purchase Confirmed</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #ffd60a 0%, #10b981 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Sustainable Mining Platform</p>\n    </div>\n\n    <div style=\"background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);\">\n        <h2 style=\"color: #10b981; margin: 0 0 20px 0; font-size: 24px;\">🎉 Mining Unit Purchase Confirmed!</h2>\n\n        <p style=\"margin: 0 0 20px 0; font-size: 16px;\">Hello <strong>{{firstName}}</strong>,</p>\n\n        <p style=\"margin: 0 0 25px 0; font-size: 16px;\">Congratulations! Your mining unit purchase has been successfully processed and is now active.</p>\n\n        <div style=\"background: #f8f9fa; padding: 25px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #10b981;\">\n            <h3 style=\"margin: 0 0 15px 0; color: #10b981; font-size: 18px;\">⚡ Mining Unit Details</h3>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">TH/s Amount:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">{{thsAmount}} TH/s</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Investment Amount:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">$\\{{investmentAmount}} USDT</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Daily ROI:</td>\n                    <td style=\"padding: 8px 0; color: #10b981; font-weight: bold;\">{{dailyROI}}%</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Purchase Date:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">{{purchaseDate}}</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Expiry Date:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">{{expiryDate}}</td>\n                </tr>\n            </table>\n        </div>\n\n        <div style=\"background: #e3f2fd; padding: 20px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #2196f3;\">\n            <h4 style=\"margin: 0 0 15px 0; color: #1976d2; font-size: 16px;\">📋 Important Information</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n                <li style=\"margin-bottom: 8px;\">Your mining unit will start generating daily returns immediately</li>\n                <li style=\"margin-bottom: 8px;\">Earnings are paid out weekly on Sundays at 00:00 AM GMT+5:30</li>\n                <li style=\"margin-bottom: 8px;\">Mining units expire after 24 months or when they reach 5x return, whichever comes first</li>\n                <li style=\"margin-bottom: 8px;\">You can track your earnings in the Mining section of your dashboard</li>\n            </ul>\n        </div>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"https://hashcorex.com/dashboard\" style=\"background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">View Dashboard</a>\n        </div>\n\n        <p style=\"margin: 25px 0 0 0; font-size: 16px;\">Thank you for choosing HashCoreX for your mining investment!</p>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br><strong>The HashCoreX Team</strong></p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Mining Unit Purchase Confirmed!\n\nHello {{firstName}},\n\nCongratulations! Your mining unit purchase has been successfully processed and is now active.\n\nMining Unit Details:\n- TH/s Amount: {{thsAmount}} TH/s\n- Investment Amount: $\\{{investmentAmount}} USDT\n- Daily ROI: {{dailyROI}}%\n- Purchase Date: {{purchaseDate}}\n- Expiry Date: {{expiryDate}}\n\nImportant Information:\n- Your mining unit will start generating daily returns immediately\n- Earnings are paid out weekly on Sundays at 00:00 AM GMT+5:30\n- Mining units expire after 24 months or when they reach 5x return, whichever comes first\n- You can track your earnings in the Mining section of your dashboard\n\nThank you for choosing HashCoreX for your mining investment!\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'mining_unit_expiry',\n        subject: 'Mining Unit Expired - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Mining Unit Expired</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #ffd60a 0%, #ff9800 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Sustainable Mining Platform</p>\n    </div>\n\n    <div style=\"background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);\">\n        <h2 style=\"color: #ff9800; margin: 0 0 20px 0; font-size: 24px;\">⏰ Mining Unit Expired</h2>\n\n        <p style=\"margin: 0 0 20px 0; font-size: 16px;\">Hello <strong>{{firstName}}</strong>,</p>\n\n        <p style=\"margin: 0 0 25px 0; font-size: 16px;\">We're writing to inform you that one of your mining units has reached its expiry condition.</p>\n\n        <div style=\"background: #fff3e0; padding: 25px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #ff9800;\">\n            <h3 style=\"margin: 0 0 15px 0; color: #ff9800; font-size: 18px;\">📊 Expired Mining Unit Details</h3>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">TH/s Amount:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">{{thsAmount}} TH/s</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Original Investment:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">$\\{{investmentAmount}} USDT</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Total Earned:</td>\n                    <td style=\"padding: 8px 0; color: #10b981; font-weight: bold;\">$\\{{totalEarned}} USDT</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Purchase Date:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">{{purchaseDate}}</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Expiry Date:</td>\n                    <td style=\"padding: 8px 0; color: #333;\">{{expiryDate}}</td>\n                </tr>\n                <tr>\n                    <td style=\"padding: 8px 0; font-weight: bold; color: #555;\">Expiry Reason:</td>\n                    <td style=\"padding: 8px 0; color: #ff9800; font-weight: bold;\">{{expiryReason}}</td>\n                </tr>\n            </table>\n        </div>\n\n        <div style=\"background: #e8f5e8; padding: 20px; margin: 25px 0; border-radius: 8px; border-left: 4px solid #2e7d32;\">\n            <h4 style=\"margin: 0 0 15px 0; color: #2e7d32; font-size: 16px;\">✅ What Happens Next?</h4>\n            <ul style=\"margin: 0; padding-left: 20px; color: #555;\">\n                <li style=\"margin-bottom: 8px;\">All accumulated earnings have been credited to your wallet</li>\n                <li style=\"margin-bottom: 8px;\">This mining unit will no longer generate daily returns</li>\n                <li style=\"margin-bottom: 8px;\">You can view the complete history in your Mining section</li>\n                <li style=\"margin-bottom: 8px;\">Consider purchasing new mining units to continue earning</li>\n            </ul>\n        </div>\n\n        <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"https://hashcorex.com/dashboard\" style=\"background: #10b981; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; margin-right: 10px;\">View Dashboard</a>\n            <a href=\"https://hashcorex.com/dashboard\" style=\"background: #ff9800; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;\">Purchase New Unit</a>\n        </div>\n\n        <p style=\"margin: 25px 0 0 0; font-size: 16px;\">Thank you for your continued trust in HashCoreX!</p>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>Best regards,<br><strong>The HashCoreX Team</strong></p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `Mining Unit Expired\n\nHello {{firstName}},\n\nWe're writing to inform you that one of your mining units has reached its expiry condition.\n\nExpired Mining Unit Details:\n- TH/s Amount: {{thsAmount}} TH/s\n- Original Investment: $\\{{investmentAmount}} USDT\n- Total Earned: $\\{{totalEarned}} USDT\n- Purchase Date: {{purchaseDate}}\n- Expiry Date: {{expiryDate}}\n- Expiry Reason: {{expiryReason}}\n\nWhat Happens Next?\n- All accumulated earnings have been credited to your wallet\n- This mining unit will no longer generate daily returns\n- You can view the complete history in your Mining section\n- Consider purchasing new mining units to continue earning\n\nThank you for your continued trust in HashCoreX!\n\nBest regards,\nThe HashCoreX Team`\n    },\n    {\n        name: 'two_factor_otp',\n        subject: 'Two-Factor Authentication Code - HashCoreX',\n        htmlContent: `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Two-Factor Authentication</title>\n</head>\n<body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;\">\n    <div style=\"background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;\">\n        <h1 style=\"color: white; margin: 0; font-size: 28px;\">HashCoreX</h1>\n        <p style=\"color: #f0f0f0; margin: 10px 0 0 0;\">Two-Factor Authentication</p>\n    </div>\n\n    <div style=\"background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px;\">\n        <h2 style=\"color: #333; margin-top: 0;\">Hello {{firstName}}!</h2>\n\n        <p>You are attempting to log in to your HashCoreX account. For your security, please use the verification code below to complete your login:</p>\n\n        <div style=\"background: #fff; border: 2px dashed #10b981; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;\">\n            <h3 style=\"margin: 0; color: #10b981; font-size: 32px; letter-spacing: 5px;\">{{otp}}</h3>\n            <p style=\"margin: 10px 0 0 0; color: #666; font-size: 14px;\">This code expires in 10 minutes</p>\n        </div>\n\n        <div style=\"background: #e7f3ff; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;\">\n            <p style=\"margin: 0; color: #1976D2;\"><strong>Security Notice:</strong></p>\n            <p style=\"margin: 5px 0 0 0; color: #666;\">If you did not attempt to log in, please ignore this email and consider changing your password for security.</p>\n        </div>\n\n        <p>This verification code is required to complete your login process. Do not share this code with anyone.</p>\n\n        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 14px;\">\n            <p>If you have any questions, please contact our support team.</p>\n            <p>Best regards,<br>The HashCoreX Team</p>\n        </div>\n    </div>\n</body>\n</html>`,\n        textContent: `HashCoreX - Two-Factor Authentication Code\n\nHello {{firstName}},\n\nYou are attempting to log in to your HashCoreX account. For your security, please use the verification code below to complete your login:\n\nVerification Code: {{otp}}\n\nThis code expires in 10 minutes.\n\nSecurity Notice:\nIf you did not attempt to log in, please ignore this email and consider changing your password for security.\n\nThis verification code is required to complete your login process. Do not share this code with anyone.\n\nIf you have any questions, please contact our support team.\n\nBest regards,\nThe HashCoreX Team`\n    }\n];\n// POST - Seed default email templates\nasync function POST(request) {\n    try {\n        // Authenticate admin\n        const { authenticated, user } = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateRequest)(request);\n        if (!authenticated || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const isUserAdmin = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.isAdmin)(user.id);\n        if (!isUserAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Admin access required'\n            }, {\n                status: 403\n            });\n        }\n        const results = [];\n        for (const template of defaultTemplates){\n            try {\n                // Check if template already exists\n                const existing = await _lib_database__WEBPACK_IMPORTED_MODULE_2__.emailTemplateDb.findByName(template.name);\n                if (existing) {\n                    results.push({\n                        name: template.name,\n                        status: 'exists'\n                    });\n                    continue;\n                }\n                // Create template\n                await _lib_database__WEBPACK_IMPORTED_MODULE_2__.emailTemplateDb.create(template);\n                results.push({\n                    name: template.name,\n                    status: 'created'\n                });\n            } catch (error) {\n                results.push({\n                    name: template.name,\n                    status: 'error',\n                    error: error.message\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Default templates seeded successfully',\n            data: results\n        });\n    } catch (error) {\n        console.error('Seed email templates error:', error);\n        await _lib_errorLogger__WEBPACK_IMPORTED_MODULE_3__.ErrorLogger.logApiError(request, error, 'SEED_EMAIL_TEMPLATES_ERROR');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/email-templates/seed/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateRequest: () => (/* binding */ authenticateRequest),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   generateReferralId: () => (/* binding */ generateReferralId),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   registerUser: () => (/* binding */ registerUser),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validateSession: () => (/* binding */ validateSession),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _envValidation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./envValidation */ \"(rsc)/./src/lib/envValidation.ts\");\n\n\n\n\n// Password utilities\nconst hashPassword = async (password)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.security.bcryptRounds());\n};\nconst verifyPassword = async (password, hashedPassword)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hashedPassword);\n};\n// JWT utilities\nconst generateToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret(), {\n        expiresIn: _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.expiresIn()\n    });\n};\nconst verifyToken = (token)=>{\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, _envValidation__WEBPACK_IMPORTED_MODULE_3__.config.jwt.secret());\n        return decoded;\n    } catch (error) {\n        return null;\n    }\n};\n// Generate unique referral ID\nconst generateReferralId = ()=>{\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = 'HC'; // HashCoreX prefix\n    for(let i = 0; i < 8; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n};\n// Authentication middleware\nconst authenticateRequest = async (request)=>{\n    const token = request.headers.get('authorization')?.replace('Bearer ', '') || request.cookies.get('auth-token')?.value;\n    if (!token) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const decoded = verifyToken(token);\n    if (!decoded) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(decoded.email);\n    if (!user) {\n        return {\n            authenticated: false,\n            user: null\n        };\n    }\n    return {\n        authenticated: true,\n        user\n    };\n};\n// User registration\nconst registerUser = async (data)=>{\n    // Check if user already exists\n    const existingUser = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (existingUser) {\n        throw new Error('User already exists with this email');\n    }\n    // Validate referral code if provided\n    let referrerId;\n    if (data.referralCode) {\n        const referrer = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(data.referralCode);\n        if (!referrer) {\n            throw new Error('Invalid referral code');\n        }\n        referrerId = referrer.id;\n    }\n    // Hash password\n    const passwordHash = await hashPassword(data.password);\n    // Generate unique referral ID\n    let referralId;\n    let isUnique = false;\n    do {\n        referralId = generateReferralId();\n        const existing = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByReferralId(referralId);\n        isUnique = !existing;\n    }while (!isUnique);\n    // Create user in PostgreSQL\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.create({\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: passwordHash,\n        referralId\n    });\n    // Create referral relationship if referrer exists\n    if (referrerId) {\n        const { placeUserByReferralType } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_referral_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./referral */ \"(rsc)/./src/lib/referral.ts\"));\n        // Determine referral type based on placementSide parameter\n        let referralType = 'general';\n        if (data.placementSide === 'left') {\n            referralType = 'left';\n        } else if (data.placementSide === 'right') {\n            referralType = 'right';\n        }\n        // Place user using the new unified placement function\n        await placeUserByReferralType(referrerId, user.id, referralType);\n    }\n    return {\n        id: user.id,\n        email: user.email,\n        referralId: user.referralId,\n        kycStatus: user.kycStatus\n    };\n};\n// User login\nconst loginUser = async (data)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findByEmail(data.email);\n    if (!user) {\n        throw new Error('Invalid email or password');\n    }\n    const isValidPassword = await verifyPassword(data.password, user.password);\n    if (!isValidPassword) {\n        throw new Error('Invalid email or password');\n    }\n    const token = generateToken({\n        userId: user.id,\n        email: user.email\n    });\n    return {\n        token,\n        user: {\n            id: user.id,\n            email: user.email,\n            referralId: user.referralId,\n            kycStatus: user.kycStatus\n        }\n    };\n};\n// Password validation\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push('Password must be at least 8 characters long');\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!/\\d/.test(password)) {\n        errors.push('Password must contain at least one number');\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push('Password must contain at least one special character');\n    }\n    return {\n        valid: errors.length === 0,\n        errors\n    };\n};\n// Email validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n// Session management\nconst createSession = (userId, email)=>{\n    return generateToken({\n        userId,\n        email\n    });\n};\nconst validateSession = (token)=>{\n    return verifyToken(token);\n};\n// Admin authentication\nconst isAdmin = async (userId)=>{\n    const user = await _database__WEBPACK_IMPORTED_MODULE_2__.userDb.findById(userId);\n    return user?.role === 'ADMIN';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    },\n    async updatePassword (id, hashedPassword) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                password: hashedPassword\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async findVerified (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: true,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/envValidation.ts":
/*!**********************************!*\
  !*** ./src/lib/envValidation.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkCriticalEnvVars: () => (/* binding */ checkCriticalEnvVars),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateStrongJWTSecret: () => (/* binding */ generateStrongJWTSecret),\n/* harmony export */   getValidatedEnv: () => (/* binding */ getValidatedEnv),\n/* harmony export */   validateEnvironment: () => (/* binding */ validateEnvironment)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/**\n * Environment Variable Validation\n * Validates all required environment variables on application startup\n */ \n// Environment validation schema\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    // Database\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DATABASE_URL must be a valid PostgreSQL URL'),\n    DIRECT_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url('DIRECT_URL must be a valid PostgreSQL URL'),\n    // JWT Configuration\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(32, 'JWT_SECRET must be at least 32 characters long').refine((secret)=>{\n        // Check for strong secret\n        const hasUpperCase = /[A-Z]/.test(secret);\n        const hasLowerCase = /[a-z]/.test(secret);\n        const hasNumbers = /\\d/.test(secret);\n        const hasSpecialChars = /[!@#$%^&*(),.?\":{}|<>]/.test(secret);\n        return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChars;\n    }, 'JWT_SECRET should contain uppercase, lowercase, numbers, and special characters'),\n    JWT_EXPIRES_IN: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('30d'),\n    // Node Environment\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'development',\n        'production',\n        'test'\n    ]).default('development'),\n    // Application Configuration\n    NEXT_PUBLIC_APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('3000'),\n    // Email Configuration (optional but validated if provided)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional(),\n    SMTP_PASSWORD: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_SECURE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').optional(),\n    // Tron Network Configuration\n    TRON_NETWORK: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'mainnet',\n        'testnet'\n    ]).default('testnet'),\n    TRON_API_KEY: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    USDT_CONTRACT_ADDRESS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    // File Upload Configuration\n    MAX_FILE_SIZE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('10485760'),\n    UPLOAD_DIR: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default('./public/uploads'),\n    // Security Configuration\n    BCRYPT_ROUNDS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('12'),\n    SESSION_TIMEOUT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('1800'),\n    // Rate Limiting Configuration\n    RATE_LIMIT_WINDOW_MS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('900000'),\n    RATE_LIMIT_MAX_REQUESTS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^\\d+$/).transform(Number).default('100'),\n    // External API Configuration\n    COINGECKO_API_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default('https://api.coingecko.com/api/v3'),\n    // Monitoring and Logging\n    LOG_LEVEL: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'error',\n        'warn',\n        'info',\n        'debug'\n    ]).default('info'),\n    ENABLE_REQUEST_LOGGING: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('false'),\n    // Feature Flags\n    ENABLE_REGISTRATION: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_KYC: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    ENABLE_WITHDRAWALS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('true'),\n    MAINTENANCE_MODE: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().transform((val)=>val === 'true').default('false')\n});\n// Conditional validation for email configuration\nconst envSchemaWithConditionals = envSchema.refine((data)=>{\n    // If any SMTP config is provided, all should be provided\n    const smtpFields = [\n        data.SMTP_HOST,\n        data.SMTP_PORT,\n        data.SMTP_USER,\n        data.SMTP_PASSWORD\n    ];\n    const hasAnySmtp = smtpFields.some((field)=>field !== undefined);\n    const hasAllSmtp = smtpFields.every((field)=>field !== undefined);\n    if (hasAnySmtp && !hasAllSmtp) {\n        return false;\n    }\n    return true;\n}, {\n    message: 'If SMTP configuration is provided, all SMTP fields (HOST, PORT, USER, PASSWORD) must be provided'\n});\n// Validate environment variables\nfunction validateEnvironment() {\n    try {\n        const result = envSchemaWithConditionals.safeParse(process.env);\n        if (!result.success) {\n            const errors = result.error.errors.map((err)=>`${err.path.join('.')}: ${err.message}`);\n            return {\n                success: false,\n                errors\n            };\n        }\n        return {\n            success: true,\n            data: result.data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            errors: [\n                'Failed to validate environment variables'\n            ]\n        };\n    }\n}\n// Get validated environment variables\nlet validatedEnv = null;\nfunction getValidatedEnv() {\n    if (!validatedEnv) {\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            process.exit(1);\n        }\n        validatedEnv = validation.data;\n        console.log('✅ Environment variables validated successfully');\n    }\n    return validatedEnv;\n}\n// Environment-specific configurations\nconst config = {\n    isDevelopment: ()=>getValidatedEnv().NODE_ENV === 'development',\n    isProduction: ()=>getValidatedEnv().NODE_ENV === 'production',\n    isTest: ()=>getValidatedEnv().NODE_ENV === 'test',\n    database: {\n        url: ()=>getValidatedEnv().DATABASE_URL,\n        directUrl: ()=>getValidatedEnv().DIRECT_URL\n    },\n    jwt: {\n        secret: ()=>getValidatedEnv().JWT_SECRET,\n        expiresIn: ()=>getValidatedEnv().JWT_EXPIRES_IN\n    },\n    server: {\n        port: ()=>getValidatedEnv().PORT,\n        appUrl: ()=>getValidatedEnv().NEXT_PUBLIC_APP_URL\n    },\n    email: {\n        isConfigured: ()=>{\n            const env = getValidatedEnv();\n            return !!(env.SMTP_HOST && env.SMTP_PORT && env.SMTP_USER && env.SMTP_PASSWORD);\n        },\n        host: ()=>getValidatedEnv().SMTP_HOST,\n        port: ()=>getValidatedEnv().SMTP_PORT,\n        user: ()=>getValidatedEnv().SMTP_USER,\n        password: ()=>getValidatedEnv().SMTP_PASSWORD,\n        secure: ()=>getValidatedEnv().SMTP_SECURE\n    },\n    tron: {\n        network: ()=>getValidatedEnv().TRON_NETWORK,\n        apiKey: ()=>getValidatedEnv().TRON_API_KEY,\n        usdtContract: ()=>getValidatedEnv().USDT_CONTRACT_ADDRESS\n    },\n    security: {\n        bcryptRounds: ()=>getValidatedEnv().BCRYPT_ROUNDS,\n        sessionTimeout: ()=>getValidatedEnv().SESSION_TIMEOUT,\n        maxFileSize: ()=>getValidatedEnv().MAX_FILE_SIZE,\n        uploadDir: ()=>getValidatedEnv().UPLOAD_DIR\n    },\n    rateLimit: {\n        windowMs: ()=>getValidatedEnv().RATE_LIMIT_WINDOW_MS,\n        maxRequests: ()=>getValidatedEnv().RATE_LIMIT_MAX_REQUESTS\n    },\n    features: {\n        registrationEnabled: ()=>getValidatedEnv().ENABLE_REGISTRATION,\n        kycEnabled: ()=>getValidatedEnv().ENABLE_KYC,\n        withdrawalsEnabled: ()=>getValidatedEnv().ENABLE_WITHDRAWALS,\n        maintenanceMode: ()=>getValidatedEnv().MAINTENANCE_MODE\n    },\n    logging: {\n        level: ()=>getValidatedEnv().LOG_LEVEL,\n        requestLogging: ()=>getValidatedEnv().ENABLE_REQUEST_LOGGING\n    },\n    external: {\n        coingeckoApiUrl: ()=>getValidatedEnv().COINGECKO_API_URL\n    }\n};\n// Validate environment on module load (server-side only)\nif (true) {\n    // Skip validation during build process\n    const isBuilding = process.env.NEXT_PHASE === 'phase-production-build';\n    if (!isBuilding) {\n        // Only validate in server environment\n        const validation = validateEnvironment();\n        if (!validation.success) {\n            console.error('❌ Environment validation failed:');\n            validation.errors?.forEach((error)=>console.error(`  - ${error}`));\n            // In development, show helpful error message\n            if (true) {\n                console.error('\\n💡 To fix these errors:');\n                console.error('1. Check your .env.local file');\n                console.error('2. Ensure all required environment variables are set');\n                console.error('3. Verify JWT_SECRET is at least 32 characters with mixed case, numbers, and special characters');\n                console.error('4. Ensure database URLs are valid PostgreSQL connection strings');\n            }\n            process.exit(1);\n        }\n        console.log('✅ Environment variables validated successfully');\n    }\n}\n// Helper function to check if all critical environment variables are set\nfunction checkCriticalEnvVars() {\n    const critical = [\n        'DATABASE_URL',\n        'DIRECT_URL',\n        'JWT_SECRET'\n    ];\n    const missing = [];\n    for (const key of critical){\n        if (!process.env[key]) {\n            missing.push(key);\n        }\n    }\n    return {\n        valid: missing.length === 0,\n        missing\n    };\n}\n// Helper function to generate a strong JWT secret\nfunction generateStrongJWTSecret() {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';\n    let result = '';\n    // Ensure at least one of each required character type\n    result += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase\n    result += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase\n    result += '0123456789'[Math.floor(Math.random() * 10)]; // Number\n    result += '!@#$%^&*()'[Math.floor(Math.random() * 10)]; // Special char\n    // Fill the rest randomly\n    for(let i = 4; i < 64; i++){\n        result += chars[Math.floor(Math.random() * chars.length)];\n    }\n    // Shuffle the string\n    return result.split('').sort(()=>Math.random() - 0.5).join('');\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2VudlZhbGlkYXRpb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOzs7Q0FHQyxHQUV1QjtBQUV4QixnQ0FBZ0M7QUFDaEMsTUFBTUMsWUFBWUQseUNBQVEsQ0FBQztJQUN6QixXQUFXO0lBQ1hHLGNBQWNILHlDQUFRLEdBQUdLLEdBQUcsQ0FBQztJQUM3QkMsWUFBWU4seUNBQVEsR0FBR0ssR0FBRyxDQUFDO0lBRTNCLG9CQUFvQjtJQUNwQkUsWUFBWVAseUNBQVEsR0FDakJRLEdBQUcsQ0FBQyxJQUFJLGtEQUNSQyxNQUFNLENBQUMsQ0FBQ0M7UUFDUCwwQkFBMEI7UUFDMUIsTUFBTUMsZUFBZSxRQUFRQyxJQUFJLENBQUNGO1FBQ2xDLE1BQU1HLGVBQWUsUUFBUUQsSUFBSSxDQUFDRjtRQUNsQyxNQUFNSSxhQUFhLEtBQUtGLElBQUksQ0FBQ0Y7UUFDN0IsTUFBTUssa0JBQWtCLHlCQUF5QkgsSUFBSSxDQUFDRjtRQUN0RCxPQUFPQyxnQkFBZ0JFLGdCQUFnQkMsY0FBY0M7SUFDdkQsR0FBRztJQUVMQyxnQkFBZ0JoQix5Q0FBUSxHQUFHaUIsT0FBTyxDQUFDO0lBRW5DLG1CQUFtQjtJQUNuQkMsVUFBVWxCLDBDQUFNLENBQUM7UUFBQztRQUFlO1FBQWM7S0FBTyxFQUFFaUIsT0FBTyxDQUFDO0lBRWhFLDRCQUE0QjtJQUM1QkcscUJBQXFCcEIseUNBQVEsR0FBR0ssR0FBRyxHQUFHZ0IsUUFBUTtJQUM5Q0MsTUFBTXRCLHlDQUFRLEdBQUd1QixLQUFLLENBQUMsU0FBU0MsU0FBUyxDQUFDQyxRQUFRUixPQUFPLENBQUM7SUFFMUQsMkRBQTJEO0lBQzNEUyxXQUFXMUIseUNBQVEsR0FBR3FCLFFBQVE7SUFDOUJNLFdBQVczQix5Q0FBUSxHQUFHdUIsS0FBSyxDQUFDLFNBQVNDLFNBQVMsQ0FBQ0MsUUFBUUosUUFBUTtJQUMvRE8sV0FBVzVCLHlDQUFRLEdBQUc2QixLQUFLLEdBQUdSLFFBQVE7SUFDdENTLGVBQWU5Qix5Q0FBUSxHQUFHcUIsUUFBUTtJQUNsQ1UsYUFBYS9CLHlDQUFRLEdBQUd3QixTQUFTLENBQUNRLENBQUFBLE1BQU9BLFFBQVEsUUFBUVgsUUFBUTtJQUVqRSw2QkFBNkI7SUFDN0JZLGNBQWNqQywwQ0FBTSxDQUFDO1FBQUM7UUFBVztLQUFVLEVBQUVpQixPQUFPLENBQUM7SUFDckRpQixjQUFjbEMseUNBQVEsR0FBR3FCLFFBQVE7SUFDakNjLHVCQUF1Qm5DLHlDQUFRLEdBQUdxQixRQUFRO0lBRTFDLDRCQUE0QjtJQUM1QmUsZUFBZXBDLHlDQUFRLEdBQUd1QixLQUFLLENBQUMsU0FBU0MsU0FBUyxDQUFDQyxRQUFRUixPQUFPLENBQUM7SUFDbkVvQixZQUFZckMseUNBQVEsR0FBR2lCLE9BQU8sQ0FBQztJQUUvQix5QkFBeUI7SUFDekJxQixlQUFldEMseUNBQVEsR0FBR3VCLEtBQUssQ0FBQyxTQUFTQyxTQUFTLENBQUNDLFFBQVFSLE9BQU8sQ0FBQztJQUNuRXNCLGlCQUFpQnZDLHlDQUFRLEdBQUd1QixLQUFLLENBQUMsU0FBU0MsU0FBUyxDQUFDQyxRQUFRUixPQUFPLENBQUM7SUFFckUsOEJBQThCO0lBQzlCdUIsc0JBQXNCeEMseUNBQVEsR0FBR3VCLEtBQUssQ0FBQyxTQUFTQyxTQUFTLENBQUNDLFFBQVFSLE9BQU8sQ0FBQztJQUMxRXdCLHlCQUF5QnpDLHlDQUFRLEdBQUd1QixLQUFLLENBQUMsU0FBU0MsU0FBUyxDQUFDQyxRQUFRUixPQUFPLENBQUM7SUFFN0UsNkJBQTZCO0lBQzdCeUIsbUJBQW1CMUMseUNBQVEsR0FBR0ssR0FBRyxHQUFHWSxPQUFPLENBQUM7SUFFNUMseUJBQXlCO0lBQ3pCMEIsV0FBVzNDLDBDQUFNLENBQUM7UUFBQztRQUFTO1FBQVE7UUFBUTtLQUFRLEVBQUVpQixPQUFPLENBQUM7SUFDOUQyQix3QkFBd0I1Qyx5Q0FBUSxHQUFHd0IsU0FBUyxDQUFDUSxDQUFBQSxNQUFPQSxRQUFRLFFBQVFmLE9BQU8sQ0FBQztJQUU1RSxnQkFBZ0I7SUFDaEI0QixxQkFBcUI3Qyx5Q0FBUSxHQUFHd0IsU0FBUyxDQUFDUSxDQUFBQSxNQUFPQSxRQUFRLFFBQVFmLE9BQU8sQ0FBQztJQUN6RTZCLFlBQVk5Qyx5Q0FBUSxHQUFHd0IsU0FBUyxDQUFDUSxDQUFBQSxNQUFPQSxRQUFRLFFBQVFmLE9BQU8sQ0FBQztJQUNoRThCLG9CQUFvQi9DLHlDQUFRLEdBQUd3QixTQUFTLENBQUNRLENBQUFBLE1BQU9BLFFBQVEsUUFBUWYsT0FBTyxDQUFDO0lBQ3hFK0Isa0JBQWtCaEQseUNBQVEsR0FBR3dCLFNBQVMsQ0FBQ1EsQ0FBQUEsTUFBT0EsUUFBUSxRQUFRZixPQUFPLENBQUM7QUFDeEU7QUFFQSxpREFBaUQ7QUFDakQsTUFBTWdDLDRCQUE0QmhELFVBQVVRLE1BQU0sQ0FBQyxDQUFDeUM7SUFDbEQseURBQXlEO0lBQ3pELE1BQU1DLGFBQWE7UUFBQ0QsS0FBS3hCLFNBQVM7UUFBRXdCLEtBQUt2QixTQUFTO1FBQUV1QixLQUFLdEIsU0FBUztRQUFFc0IsS0FBS3BCLGFBQWE7S0FBQztJQUN2RixNQUFNc0IsYUFBYUQsV0FBV0UsSUFBSSxDQUFDQyxDQUFBQSxRQUFTQSxVQUFVQztJQUN0RCxNQUFNQyxhQUFhTCxXQUFXTSxLQUFLLENBQUNILENBQUFBLFFBQVNBLFVBQVVDO0lBRXZELElBQUlILGNBQWMsQ0FBQ0ksWUFBWTtRQUM3QixPQUFPO0lBQ1Q7SUFFQSxPQUFPO0FBQ1QsR0FBRztJQUNERSxTQUFTO0FBQ1g7QUFZQSxpQ0FBaUM7QUFDMUIsU0FBU0M7SUFDZCxJQUFJO1FBQ0YsTUFBTUMsU0FBU1gsMEJBQTBCWSxTQUFTLENBQUNDLFFBQVFDLEdBQUc7UUFFOUQsSUFBSSxDQUFDSCxPQUFPSSxPQUFPLEVBQUU7WUFDbkIsTUFBTUMsU0FBU0wsT0FBT00sS0FBSyxDQUFDRCxNQUFNLENBQUNFLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFDckMsR0FBR0EsSUFBSUMsSUFBSSxDQUFDQyxJQUFJLENBQUMsS0FBSyxFQUFFLEVBQUVGLElBQUlWLE9BQU8sRUFBRTtZQUd6QyxPQUFPO2dCQUNMTSxTQUFTO2dCQUNUQztZQUNGO1FBQ0Y7UUFFQSxPQUFPO1lBQ0xELFNBQVM7WUFDVGQsTUFBTVUsT0FBT1YsSUFBSTtRQUNuQjtJQUNGLEVBQUUsT0FBT2dCLE9BQU87UUFDZCxPQUFPO1lBQ0xGLFNBQVM7WUFDVEMsUUFBUTtnQkFBQzthQUEyQztRQUN0RDtJQUNGO0FBQ0Y7QUFFQSxzQ0FBc0M7QUFDdEMsSUFBSU0sZUFBb0M7QUFFakMsU0FBU0M7SUFDZCxJQUFJLENBQUNELGNBQWM7UUFDakIsTUFBTUUsYUFBYWQ7UUFFbkIsSUFBSSxDQUFDYyxXQUFXVCxPQUFPLEVBQUU7WUFDdkJVLFFBQVFSLEtBQUssQ0FBQztZQUNkTyxXQUFXUixNQUFNLEVBQUVVLFFBQVFULENBQUFBLFFBQVNRLFFBQVFSLEtBQUssQ0FBQyxDQUFDLElBQUksRUFBRUEsT0FBTztZQUNoRUosUUFBUWMsSUFBSSxDQUFDO1FBQ2Y7UUFFQUwsZUFBZUUsV0FBV3ZCLElBQUk7UUFDOUJ3QixRQUFRRyxHQUFHLENBQUM7SUFDZDtJQUVBLE9BQU9OO0FBQ1Q7QUFFQSxzQ0FBc0M7QUFDL0IsTUFBTU8sU0FBUztJQUNwQkMsZUFBZSxJQUFNUCxrQkFBa0J0RCxRQUFRLEtBQUs7SUFDcEQ4RCxjQUFjLElBQU1SLGtCQUFrQnRELFFBQVEsS0FBSztJQUNuRCtELFFBQVEsSUFBTVQsa0JBQWtCdEQsUUFBUSxLQUFLO0lBRTdDZ0UsVUFBVTtRQUNSN0UsS0FBSyxJQUFNbUUsa0JBQWtCckUsWUFBWTtRQUN6Q2dGLFdBQVcsSUFBTVgsa0JBQWtCbEUsVUFBVTtJQUMvQztJQUVBOEUsS0FBSztRQUNIMUUsUUFBUSxJQUFNOEQsa0JBQWtCakUsVUFBVTtRQUMxQzhFLFdBQVcsSUFBTWIsa0JBQWtCeEQsY0FBYztJQUNuRDtJQUVBc0UsUUFBUTtRQUNOQyxNQUFNLElBQU1mLGtCQUFrQmxELElBQUk7UUFDbENrRSxRQUFRLElBQU1oQixrQkFBa0JwRCxtQkFBbUI7SUFDckQ7SUFFQVMsT0FBTztRQUNMNEQsY0FBYztZQUNaLE1BQU0xQixNQUFNUztZQUNaLE9BQU8sQ0FBQyxDQUFFVCxDQUFBQSxJQUFJckMsU0FBUyxJQUFJcUMsSUFBSXBDLFNBQVMsSUFBSW9DLElBQUluQyxTQUFTLElBQUltQyxJQUFJakMsYUFBYTtRQUNoRjtRQUNBNEQsTUFBTSxJQUFNbEIsa0JBQWtCOUMsU0FBUztRQUN2QzZELE1BQU0sSUFBTWYsa0JBQWtCN0MsU0FBUztRQUN2Q2dFLE1BQU0sSUFBTW5CLGtCQUFrQjVDLFNBQVM7UUFDdkNnRSxVQUFVLElBQU1wQixrQkFBa0IxQyxhQUFhO1FBQy9DK0QsUUFBUSxJQUFNckIsa0JBQWtCekMsV0FBVztJQUM3QztJQUVBK0QsTUFBTTtRQUNKQyxTQUFTLElBQU12QixrQkFBa0J2QyxZQUFZO1FBQzdDK0QsUUFBUSxJQUFNeEIsa0JBQWtCdEMsWUFBWTtRQUM1QytELGNBQWMsSUFBTXpCLGtCQUFrQnJDLHFCQUFxQjtJQUM3RDtJQUVBK0QsVUFBVTtRQUNSQyxjQUFjLElBQU0zQixrQkFBa0JsQyxhQUFhO1FBQ25EOEQsZ0JBQWdCLElBQU01QixrQkFBa0JqQyxlQUFlO1FBQ3ZEOEQsYUFBYSxJQUFNN0Isa0JBQWtCcEMsYUFBYTtRQUNsRGtFLFdBQVcsSUFBTTlCLGtCQUFrQm5DLFVBQVU7SUFDL0M7SUFFQWtFLFdBQVc7UUFDVEMsVUFBVSxJQUFNaEMsa0JBQWtCaEMsb0JBQW9CO1FBQ3REaUUsYUFBYSxJQUFNakMsa0JBQWtCL0IsdUJBQXVCO0lBQzlEO0lBRUFpRSxVQUFVO1FBQ1JDLHFCQUFxQixJQUFNbkMsa0JBQWtCM0IsbUJBQW1CO1FBQ2hFK0QsWUFBWSxJQUFNcEMsa0JBQWtCMUIsVUFBVTtRQUM5QytELG9CQUFvQixJQUFNckMsa0JBQWtCekIsa0JBQWtCO1FBQzlEK0QsaUJBQWlCLElBQU10QyxrQkFBa0J4QixnQkFBZ0I7SUFDM0Q7SUFFQStELFNBQVM7UUFDUEMsT0FBTyxJQUFNeEMsa0JBQWtCN0IsU0FBUztRQUN4Q3NFLGdCQUFnQixJQUFNekMsa0JBQWtCNUIsc0JBQXNCO0lBQ2hFO0lBRUFzRSxVQUFVO1FBQ1JDLGlCQUFpQixJQUFNM0Msa0JBQWtCOUIsaUJBQWlCO0lBQzVEO0FBQ0YsRUFBRTtBQUVGLHlEQUF5RDtBQUN6RCxJQUFJLElBQWdFLEVBQUU7SUFDcEUsdUNBQXVDO0lBQ3ZDLE1BQU0wRSxhQUFhdEQsUUFBUUMsR0FBRyxDQUFDc0QsVUFBVSxLQUFLO0lBRTlDLElBQUksQ0FBQ0QsWUFBWTtRQUNmLHNDQUFzQztRQUN0QyxNQUFNM0MsYUFBYWQ7UUFFbkIsSUFBSSxDQUFDYyxXQUFXVCxPQUFPLEVBQUU7WUFDdkJVLFFBQVFSLEtBQUssQ0FBQztZQUNkTyxXQUFXUixNQUFNLEVBQUVVLFFBQVFULENBQUFBLFFBQVNRLFFBQVFSLEtBQUssQ0FBQyxDQUFDLElBQUksRUFBRUEsT0FBTztZQUVoRSw2Q0FBNkM7WUFDN0MsSUFBSUosSUFBc0MsRUFBRTtnQkFDMUNZLFFBQVFSLEtBQUssQ0FBQztnQkFDZFEsUUFBUVIsS0FBSyxDQUFDO2dCQUNkUSxRQUFRUixLQUFLLENBQUM7Z0JBQ2RRLFFBQVFSLEtBQUssQ0FBQztnQkFDZFEsUUFBUVIsS0FBSyxDQUFDO1lBQ2hCO1lBRUFKLFFBQVFjLElBQUksQ0FBQztRQUNmO1FBRUFGLFFBQVFHLEdBQUcsQ0FBQztJQUNkO0FBQ0Y7QUFFQSx5RUFBeUU7QUFDbEUsU0FBU3lDO0lBQ2QsTUFBTUMsV0FBVztRQUFDO1FBQWdCO1FBQWM7S0FBYTtJQUM3RCxNQUFNQyxVQUFvQixFQUFFO0lBRTVCLEtBQUssTUFBTUMsT0FBT0YsU0FBVTtRQUMxQixJQUFJLENBQUN6RCxRQUFRQyxHQUFHLENBQUMwRCxJQUFJLEVBQUU7WUFDckJELFFBQVFFLElBQUksQ0FBQ0Q7UUFDZjtJQUNGO0lBRUEsT0FBTztRQUNMRSxPQUFPSCxRQUFRSSxNQUFNLEtBQUs7UUFDMUJKO0lBQ0Y7QUFDRjtBQUVBLGtEQUFrRDtBQUMzQyxTQUFTSztJQUNkLE1BQU1DLFFBQVE7SUFDZCxJQUFJbEUsU0FBUztJQUViLHNEQUFzRDtJQUN0REEsVUFBVSw0QkFBNEIsQ0FBQ21FLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLElBQUksRUFBRSxZQUFZO0lBQ3BGckUsVUFBVSw0QkFBNEIsQ0FBQ21FLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLElBQUksRUFBRSxZQUFZO0lBQ3BGckUsVUFBVSxZQUFZLENBQUNtRSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxJQUFJLEVBQUUsU0FBUztJQUNqRXJFLFVBQVUsWUFBWSxDQUFDbUUsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssSUFBSSxFQUFFLGVBQWU7SUFFdkUseUJBQXlCO0lBQ3pCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJLElBQUlBLElBQUs7UUFDM0J0RSxVQUFVa0UsS0FBSyxDQUFDQyxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS0gsTUFBTUYsTUFBTSxFQUFFO0lBQzNEO0lBRUEscUJBQXFCO0lBQ3JCLE9BQU9oRSxPQUFPdUUsS0FBSyxDQUFDLElBQUlDLElBQUksQ0FBQyxJQUFNTCxLQUFLRSxNQUFNLEtBQUssS0FBSzNELElBQUksQ0FBQztBQUMvRDtBQUVBLGlFQUFlUSxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRyZWFtXFxEZXNrdG9wXFxIYXNoX01pbmluZ3NcXGhhc2hjb3JleFxcc3JjXFxsaWJcXGVudlZhbGlkYXRpb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFbnZpcm9ubWVudCBWYXJpYWJsZSBWYWxpZGF0aW9uXG4gKiBWYWxpZGF0ZXMgYWxsIHJlcXVpcmVkIGVudmlyb25tZW50IHZhcmlhYmxlcyBvbiBhcHBsaWNhdGlvbiBzdGFydHVwXG4gKi9cblxuaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCc7XG5cbi8vIEVudmlyb25tZW50IHZhbGlkYXRpb24gc2NoZW1hXG5jb25zdCBlbnZTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIC8vIERhdGFiYXNlXG4gIERBVEFCQVNFX1VSTDogei5zdHJpbmcoKS51cmwoJ0RBVEFCQVNFX1VSTCBtdXN0IGJlIGEgdmFsaWQgUG9zdGdyZVNRTCBVUkwnKSxcbiAgRElSRUNUX1VSTDogei5zdHJpbmcoKS51cmwoJ0RJUkVDVF9VUkwgbXVzdCBiZSBhIHZhbGlkIFBvc3RncmVTUUwgVVJMJyksXG4gIFxuICAvLyBKV1QgQ29uZmlndXJhdGlvblxuICBKV1RfU0VDUkVUOiB6LnN0cmluZygpXG4gICAgLm1pbigzMiwgJ0pXVF9TRUNSRVQgbXVzdCBiZSBhdCBsZWFzdCAzMiBjaGFyYWN0ZXJzIGxvbmcnKVxuICAgIC5yZWZpbmUoKHNlY3JldCkgPT4ge1xuICAgICAgLy8gQ2hlY2sgZm9yIHN0cm9uZyBzZWNyZXRcbiAgICAgIGNvbnN0IGhhc1VwcGVyQ2FzZSA9IC9bQS1aXS8udGVzdChzZWNyZXQpO1xuICAgICAgY29uc3QgaGFzTG93ZXJDYXNlID0gL1thLXpdLy50ZXN0KHNlY3JldCk7XG4gICAgICBjb25zdCBoYXNOdW1iZXJzID0gL1xcZC8udGVzdChzZWNyZXQpO1xuICAgICAgY29uc3QgaGFzU3BlY2lhbENoYXJzID0gL1shQCMkJV4mKigpLC4/XCI6e318PD5dLy50ZXN0KHNlY3JldCk7XG4gICAgICByZXR1cm4gaGFzVXBwZXJDYXNlICYmIGhhc0xvd2VyQ2FzZSAmJiBoYXNOdW1iZXJzICYmIGhhc1NwZWNpYWxDaGFycztcbiAgICB9LCAnSldUX1NFQ1JFVCBzaG91bGQgY29udGFpbiB1cHBlcmNhc2UsIGxvd2VyY2FzZSwgbnVtYmVycywgYW5kIHNwZWNpYWwgY2hhcmFjdGVycycpLFxuICBcbiAgSldUX0VYUElSRVNfSU46IHouc3RyaW5nKCkuZGVmYXVsdCgnMzBkJyksXG4gIFxuICAvLyBOb2RlIEVudmlyb25tZW50XG4gIE5PREVfRU5WOiB6LmVudW0oWydkZXZlbG9wbWVudCcsICdwcm9kdWN0aW9uJywgJ3Rlc3QnXSkuZGVmYXVsdCgnZGV2ZWxvcG1lbnQnKSxcbiAgXG4gIC8vIEFwcGxpY2F0aW9uIENvbmZpZ3VyYXRpb25cbiAgTkVYVF9QVUJMSUNfQVBQX1VSTDogei5zdHJpbmcoKS51cmwoKS5vcHRpb25hbCgpLFxuICBQT1JUOiB6LnN0cmluZygpLnJlZ2V4KC9eXFxkKyQvKS50cmFuc2Zvcm0oTnVtYmVyKS5kZWZhdWx0KCczMDAwJyksXG4gIFxuICAvLyBFbWFpbCBDb25maWd1cmF0aW9uIChvcHRpb25hbCBidXQgdmFsaWRhdGVkIGlmIHByb3ZpZGVkKVxuICBTTVRQX0hPU1Q6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgU01UUF9QT1JUOiB6LnN0cmluZygpLnJlZ2V4KC9eXFxkKyQvKS50cmFuc2Zvcm0oTnVtYmVyKS5vcHRpb25hbCgpLFxuICBTTVRQX1VTRVI6IHouc3RyaW5nKCkuZW1haWwoKS5vcHRpb25hbCgpLFxuICBTTVRQX1BBU1NXT1JEOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIFNNVFBfU0VDVVJFOiB6LnN0cmluZygpLnRyYW5zZm9ybSh2YWwgPT4gdmFsID09PSAndHJ1ZScpLm9wdGlvbmFsKCksXG4gIFxuICAvLyBUcm9uIE5ldHdvcmsgQ29uZmlndXJhdGlvblxuICBUUk9OX05FVFdPUks6IHouZW51bShbJ21haW5uZXQnLCAndGVzdG5ldCddKS5kZWZhdWx0KCd0ZXN0bmV0JyksXG4gIFRST05fQVBJX0tFWTogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBVU0RUX0NPTlRSQUNUX0FERFJFU1M6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgXG4gIC8vIEZpbGUgVXBsb2FkIENvbmZpZ3VyYXRpb25cbiAgTUFYX0ZJTEVfU0laRTogei5zdHJpbmcoKS5yZWdleCgvXlxcZCskLykudHJhbnNmb3JtKE51bWJlcikuZGVmYXVsdCgnMTA0ODU3NjAnKSwgLy8gMTBNQlxuICBVUExPQURfRElSOiB6LnN0cmluZygpLmRlZmF1bHQoJy4vcHVibGljL3VwbG9hZHMnKSxcbiAgXG4gIC8vIFNlY3VyaXR5IENvbmZpZ3VyYXRpb25cbiAgQkNSWVBUX1JPVU5EUzogei5zdHJpbmcoKS5yZWdleCgvXlxcZCskLykudHJhbnNmb3JtKE51bWJlcikuZGVmYXVsdCgnMTInKSxcbiAgU0VTU0lPTl9USU1FT1VUOiB6LnN0cmluZygpLnJlZ2V4KC9eXFxkKyQvKS50cmFuc2Zvcm0oTnVtYmVyKS5kZWZhdWx0KCcxODAwJyksIC8vIDMwIG1pbnV0ZXNcbiAgXG4gIC8vIFJhdGUgTGltaXRpbmcgQ29uZmlndXJhdGlvblxuICBSQVRFX0xJTUlUX1dJTkRPV19NUzogei5zdHJpbmcoKS5yZWdleCgvXlxcZCskLykudHJhbnNmb3JtKE51bWJlcikuZGVmYXVsdCgnOTAwMDAwJyksIC8vIDE1IG1pbnV0ZXNcbiAgUkFURV9MSU1JVF9NQVhfUkVRVUVTVFM6IHouc3RyaW5nKCkucmVnZXgoL15cXGQrJC8pLnRyYW5zZm9ybShOdW1iZXIpLmRlZmF1bHQoJzEwMCcpLFxuICBcbiAgLy8gRXh0ZXJuYWwgQVBJIENvbmZpZ3VyYXRpb25cbiAgQ09JTkdFQ0tPX0FQSV9VUkw6IHouc3RyaW5nKCkudXJsKCkuZGVmYXVsdCgnaHR0cHM6Ly9hcGkuY29pbmdlY2tvLmNvbS9hcGkvdjMnKSxcbiAgXG4gIC8vIE1vbml0b3JpbmcgYW5kIExvZ2dpbmdcbiAgTE9HX0xFVkVMOiB6LmVudW0oWydlcnJvcicsICd3YXJuJywgJ2luZm8nLCAnZGVidWcnXSkuZGVmYXVsdCgnaW5mbycpLFxuICBFTkFCTEVfUkVRVUVTVF9MT0dHSU5HOiB6LnN0cmluZygpLnRyYW5zZm9ybSh2YWwgPT4gdmFsID09PSAndHJ1ZScpLmRlZmF1bHQoJ2ZhbHNlJyksXG4gIFxuICAvLyBGZWF0dXJlIEZsYWdzXG4gIEVOQUJMRV9SRUdJU1RSQVRJT046IHouc3RyaW5nKCkudHJhbnNmb3JtKHZhbCA9PiB2YWwgPT09ICd0cnVlJykuZGVmYXVsdCgndHJ1ZScpLFxuICBFTkFCTEVfS1lDOiB6LnN0cmluZygpLnRyYW5zZm9ybSh2YWwgPT4gdmFsID09PSAndHJ1ZScpLmRlZmF1bHQoJ3RydWUnKSxcbiAgRU5BQkxFX1dJVEhEUkFXQUxTOiB6LnN0cmluZygpLnRyYW5zZm9ybSh2YWwgPT4gdmFsID09PSAndHJ1ZScpLmRlZmF1bHQoJ3RydWUnKSxcbiAgTUFJTlRFTkFOQ0VfTU9ERTogei5zdHJpbmcoKS50cmFuc2Zvcm0odmFsID0+IHZhbCA9PT0gJ3RydWUnKS5kZWZhdWx0KCdmYWxzZScpLFxufSk7XG5cbi8vIENvbmRpdGlvbmFsIHZhbGlkYXRpb24gZm9yIGVtYWlsIGNvbmZpZ3VyYXRpb25cbmNvbnN0IGVudlNjaGVtYVdpdGhDb25kaXRpb25hbHMgPSBlbnZTY2hlbWEucmVmaW5lKChkYXRhKSA9PiB7XG4gIC8vIElmIGFueSBTTVRQIGNvbmZpZyBpcyBwcm92aWRlZCwgYWxsIHNob3VsZCBiZSBwcm92aWRlZFxuICBjb25zdCBzbXRwRmllbGRzID0gW2RhdGEuU01UUF9IT1NULCBkYXRhLlNNVFBfUE9SVCwgZGF0YS5TTVRQX1VTRVIsIGRhdGEuU01UUF9QQVNTV09SRF07XG4gIGNvbnN0IGhhc0FueVNtdHAgPSBzbXRwRmllbGRzLnNvbWUoZmllbGQgPT4gZmllbGQgIT09IHVuZGVmaW5lZCk7XG4gIGNvbnN0IGhhc0FsbFNtdHAgPSBzbXRwRmllbGRzLmV2ZXJ5KGZpZWxkID0+IGZpZWxkICE9PSB1bmRlZmluZWQpO1xuICBcbiAgaWYgKGhhc0FueVNtdHAgJiYgIWhhc0FsbFNtdHApIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgXG4gIHJldHVybiB0cnVlO1xufSwge1xuICBtZXNzYWdlOiAnSWYgU01UUCBjb25maWd1cmF0aW9uIGlzIHByb3ZpZGVkLCBhbGwgU01UUCBmaWVsZHMgKEhPU1QsIFBPUlQsIFVTRVIsIFBBU1NXT1JEKSBtdXN0IGJlIHByb3ZpZGVkJyxcbn0pO1xuXG4vLyBUeXBlIGZvciB2YWxpZGF0ZWQgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5leHBvcnQgdHlwZSBWYWxpZGF0ZWRFbnYgPSB6LmluZmVyPHR5cGVvZiBlbnZTY2hlbWE+O1xuXG4vLyBWYWxpZGF0aW9uIHJlc3VsdFxuaW50ZXJmYWNlIEVudlZhbGlkYXRpb25SZXN1bHQge1xuICBzdWNjZXNzOiBib29sZWFuO1xuICBkYXRhPzogVmFsaWRhdGVkRW52O1xuICBlcnJvcnM/OiBzdHJpbmdbXTtcbn1cblxuLy8gVmFsaWRhdGUgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5leHBvcnQgZnVuY3Rpb24gdmFsaWRhdGVFbnZpcm9ubWVudCgpOiBFbnZWYWxpZGF0aW9uUmVzdWx0IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHQgPSBlbnZTY2hlbWFXaXRoQ29uZGl0aW9uYWxzLnNhZmVQYXJzZShwcm9jZXNzLmVudik7XG4gICAgXG4gICAgaWYgKCFyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgY29uc3QgZXJyb3JzID0gcmVzdWx0LmVycm9yLmVycm9ycy5tYXAoZXJyID0+IFxuICAgICAgICBgJHtlcnIucGF0aC5qb2luKCcuJyl9OiAke2Vyci5tZXNzYWdlfWBcbiAgICAgICk7XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBlcnJvcnMsXG4gICAgICB9O1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHJlc3VsdC5kYXRhLFxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3JzOiBbJ0ZhaWxlZCB0byB2YWxpZGF0ZSBlbnZpcm9ubWVudCB2YXJpYWJsZXMnXSxcbiAgICB9O1xuICB9XG59XG5cbi8vIEdldCB2YWxpZGF0ZWQgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5sZXQgdmFsaWRhdGVkRW52OiBWYWxpZGF0ZWRFbnYgfCBudWxsID0gbnVsbDtcblxuZXhwb3J0IGZ1bmN0aW9uIGdldFZhbGlkYXRlZEVudigpOiBWYWxpZGF0ZWRFbnYge1xuICBpZiAoIXZhbGlkYXRlZEVudikge1xuICAgIGNvbnN0IHZhbGlkYXRpb24gPSB2YWxpZGF0ZUVudmlyb25tZW50KCk7XG4gICAgXG4gICAgaWYgKCF2YWxpZGF0aW9uLnN1Y2Nlc3MpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFbnZpcm9ubWVudCB2YWxpZGF0aW9uIGZhaWxlZDonKTtcbiAgICAgIHZhbGlkYXRpb24uZXJyb3JzPy5mb3JFYWNoKGVycm9yID0+IGNvbnNvbGUuZXJyb3IoYCAgLSAke2Vycm9yfWApKTtcbiAgICAgIHByb2Nlc3MuZXhpdCgxKTtcbiAgICB9XG4gICAgXG4gICAgdmFsaWRhdGVkRW52ID0gdmFsaWRhdGlvbi5kYXRhITtcbiAgICBjb25zb2xlLmxvZygn4pyFIEVudmlyb25tZW50IHZhcmlhYmxlcyB2YWxpZGF0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gIH1cbiAgXG4gIHJldHVybiB2YWxpZGF0ZWRFbnY7XG59XG5cbi8vIEVudmlyb25tZW50LXNwZWNpZmljIGNvbmZpZ3VyYXRpb25zXG5leHBvcnQgY29uc3QgY29uZmlnID0ge1xuICBpc0RldmVsb3BtZW50OiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyxcbiAgaXNQcm9kdWN0aW9uOiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nLFxuICBpc1Rlc3Q6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLk5PREVfRU5WID09PSAndGVzdCcsXG4gIFxuICBkYXRhYmFzZToge1xuICAgIHVybDogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuREFUQUJBU0VfVVJMLFxuICAgIGRpcmVjdFVybDogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuRElSRUNUX1VSTCxcbiAgfSxcbiAgXG4gIGp3dDoge1xuICAgIHNlY3JldDogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuSldUX1NFQ1JFVCxcbiAgICBleHBpcmVzSW46ICgpID0+IGdldFZhbGlkYXRlZEVudigpLkpXVF9FWFBJUkVTX0lOLFxuICB9LFxuICBcbiAgc2VydmVyOiB7XG4gICAgcG9ydDogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuUE9SVCxcbiAgICBhcHBVcmw6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLk5FWFRfUFVCTElDX0FQUF9VUkwsXG4gIH0sXG4gIFxuICBlbWFpbDoge1xuICAgIGlzQ29uZmlndXJlZDogKCkgPT4ge1xuICAgICAgY29uc3QgZW52ID0gZ2V0VmFsaWRhdGVkRW52KCk7XG4gICAgICByZXR1cm4gISEoZW52LlNNVFBfSE9TVCAmJiBlbnYuU01UUF9QT1JUICYmIGVudi5TTVRQX1VTRVIgJiYgZW52LlNNVFBfUEFTU1dPUkQpO1xuICAgIH0sXG4gICAgaG9zdDogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuU01UUF9IT1NULFxuICAgIHBvcnQ6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLlNNVFBfUE9SVCxcbiAgICB1c2VyOiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5TTVRQX1VTRVIsXG4gICAgcGFzc3dvcmQ6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLlNNVFBfUEFTU1dPUkQsXG4gICAgc2VjdXJlOiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5TTVRQX1NFQ1VSRSxcbiAgfSxcbiAgXG4gIHRyb246IHtcbiAgICBuZXR3b3JrOiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5UUk9OX05FVFdPUkssXG4gICAgYXBpS2V5OiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5UUk9OX0FQSV9LRVksXG4gICAgdXNkdENvbnRyYWN0OiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5VU0RUX0NPTlRSQUNUX0FERFJFU1MsXG4gIH0sXG4gIFxuICBzZWN1cml0eToge1xuICAgIGJjcnlwdFJvdW5kczogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuQkNSWVBUX1JPVU5EUyxcbiAgICBzZXNzaW9uVGltZW91dDogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuU0VTU0lPTl9USU1FT1VULFxuICAgIG1heEZpbGVTaXplOiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5NQVhfRklMRV9TSVpFLFxuICAgIHVwbG9hZERpcjogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuVVBMT0FEX0RJUixcbiAgfSxcbiAgXG4gIHJhdGVMaW1pdDoge1xuICAgIHdpbmRvd01zOiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5SQVRFX0xJTUlUX1dJTkRPV19NUyxcbiAgICBtYXhSZXF1ZXN0czogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuUkFURV9MSU1JVF9NQVhfUkVRVUVTVFMsXG4gIH0sXG4gIFxuICBmZWF0dXJlczoge1xuICAgIHJlZ2lzdHJhdGlvbkVuYWJsZWQ6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLkVOQUJMRV9SRUdJU1RSQVRJT04sXG4gICAga3ljRW5hYmxlZDogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuRU5BQkxFX0tZQyxcbiAgICB3aXRoZHJhd2Fsc0VuYWJsZWQ6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLkVOQUJMRV9XSVRIRFJBV0FMUyxcbiAgICBtYWludGVuYW5jZU1vZGU6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLk1BSU5URU5BTkNFX01PREUsXG4gIH0sXG4gIFxuICBsb2dnaW5nOiB7XG4gICAgbGV2ZWw6ICgpID0+IGdldFZhbGlkYXRlZEVudigpLkxPR19MRVZFTCxcbiAgICByZXF1ZXN0TG9nZ2luZzogKCkgPT4gZ2V0VmFsaWRhdGVkRW52KCkuRU5BQkxFX1JFUVVFU1RfTE9HR0lORyxcbiAgfSxcbiAgXG4gIGV4dGVybmFsOiB7XG4gICAgY29pbmdlY2tvQXBpVXJsOiAoKSA9PiBnZXRWYWxpZGF0ZWRFbnYoKS5DT0lOR0VDS09fQVBJX1VSTCxcbiAgfSxcbn07XG5cbi8vIFZhbGlkYXRlIGVudmlyb25tZW50IG9uIG1vZHVsZSBsb2FkIChzZXJ2ZXItc2lkZSBvbmx5KVxuaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnICYmIHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAndGVzdCcpIHtcbiAgLy8gU2tpcCB2YWxpZGF0aW9uIGR1cmluZyBidWlsZCBwcm9jZXNzXG4gIGNvbnN0IGlzQnVpbGRpbmcgPSBwcm9jZXNzLmVudi5ORVhUX1BIQVNFID09PSAncGhhc2UtcHJvZHVjdGlvbi1idWlsZCc7XG5cbiAgaWYgKCFpc0J1aWxkaW5nKSB7XG4gICAgLy8gT25seSB2YWxpZGF0ZSBpbiBzZXJ2ZXIgZW52aXJvbm1lbnRcbiAgICBjb25zdCB2YWxpZGF0aW9uID0gdmFsaWRhdGVFbnZpcm9ubWVudCgpO1xuXG4gICAgaWYgKCF2YWxpZGF0aW9uLnN1Y2Nlc3MpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFbnZpcm9ubWVudCB2YWxpZGF0aW9uIGZhaWxlZDonKTtcbiAgICAgIHZhbGlkYXRpb24uZXJyb3JzPy5mb3JFYWNoKGVycm9yID0+IGNvbnNvbGUuZXJyb3IoYCAgLSAke2Vycm9yfWApKTtcblxuICAgICAgLy8gSW4gZGV2ZWxvcG1lbnQsIHNob3cgaGVscGZ1bCBlcnJvciBtZXNzYWdlXG4gICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignXFxu8J+SoSBUbyBmaXggdGhlc2UgZXJyb3JzOicpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCcxLiBDaGVjayB5b3VyIC5lbnYubG9jYWwgZmlsZScpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCcyLiBFbnN1cmUgYWxsIHJlcXVpcmVkIGVudmlyb25tZW50IHZhcmlhYmxlcyBhcmUgc2V0Jyk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJzMuIFZlcmlmeSBKV1RfU0VDUkVUIGlzIGF0IGxlYXN0IDMyIGNoYXJhY3RlcnMgd2l0aCBtaXhlZCBjYXNlLCBudW1iZXJzLCBhbmQgc3BlY2lhbCBjaGFyYWN0ZXJzJyk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJzQuIEVuc3VyZSBkYXRhYmFzZSBVUkxzIGFyZSB2YWxpZCBQb3N0Z3JlU1FMIGNvbm5lY3Rpb24gc3RyaW5ncycpO1xuICAgICAgfVxuXG4gICAgICBwcm9jZXNzLmV4aXQoMSk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ+KchSBFbnZpcm9ubWVudCB2YXJpYWJsZXMgdmFsaWRhdGVkIHN1Y2Nlc3NmdWxseScpO1xuICB9XG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBjaGVjayBpZiBhbGwgY3JpdGljYWwgZW52aXJvbm1lbnQgdmFyaWFibGVzIGFyZSBzZXRcbmV4cG9ydCBmdW5jdGlvbiBjaGVja0NyaXRpY2FsRW52VmFycygpOiB7IHZhbGlkOiBib29sZWFuOyBtaXNzaW5nOiBzdHJpbmdbXSB9IHtcbiAgY29uc3QgY3JpdGljYWwgPSBbJ0RBVEFCQVNFX1VSTCcsICdESVJFQ1RfVVJMJywgJ0pXVF9TRUNSRVQnXTtcbiAgY29uc3QgbWlzc2luZzogc3RyaW5nW10gPSBbXTtcbiAgXG4gIGZvciAoY29uc3Qga2V5IG9mIGNyaXRpY2FsKSB7XG4gICAgaWYgKCFwcm9jZXNzLmVudltrZXldKSB7XG4gICAgICBtaXNzaW5nLnB1c2goa2V5KTtcbiAgICB9XG4gIH1cbiAgXG4gIHJldHVybiB7XG4gICAgdmFsaWQ6IG1pc3NpbmcubGVuZ3RoID09PSAwLFxuICAgIG1pc3NpbmcsXG4gIH07XG59XG5cbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZW5lcmF0ZSBhIHN0cm9uZyBKV1Qgc2VjcmV0XG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVTdHJvbmdKV1RTZWNyZXQoKTogc3RyaW5nIHtcbiAgY29uc3QgY2hhcnMgPSAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkhQCMkJV4mKigpJztcbiAgbGV0IHJlc3VsdCA9ICcnO1xuICBcbiAgLy8gRW5zdXJlIGF0IGxlYXN0IG9uZSBvZiBlYWNoIHJlcXVpcmVkIGNoYXJhY3RlciB0eXBlXG4gIHJlc3VsdCArPSAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVonW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDI2KV07IC8vIFVwcGVyY2FzZVxuICByZXN1bHQgKz0gJ2FiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6J1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAyNildOyAvLyBMb3dlcmNhc2VcbiAgcmVzdWx0ICs9ICcwMTIzNDU2Nzg5J1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMCldOyAvLyBOdW1iZXJcbiAgcmVzdWx0ICs9ICchQCMkJV4mKigpJ1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAxMCldOyAvLyBTcGVjaWFsIGNoYXJcbiAgXG4gIC8vIEZpbGwgdGhlIHJlc3QgcmFuZG9tbHlcbiAgZm9yIChsZXQgaSA9IDQ7IGkgPCA2NDsgaSsrKSB7XG4gICAgcmVzdWx0ICs9IGNoYXJzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGNoYXJzLmxlbmd0aCldO1xuICB9XG4gIFxuICAvLyBTaHVmZmxlIHRoZSBzdHJpbmdcbiAgcmV0dXJuIHJlc3VsdC5zcGxpdCgnJykuc29ydCgoKSA9PiBNYXRoLnJhbmRvbSgpIC0gMC41KS5qb2luKCcnKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgY29uZmlnO1xuIl0sIm5hbWVzIjpbInoiLCJlbnZTY2hlbWEiLCJvYmplY3QiLCJEQVRBQkFTRV9VUkwiLCJzdHJpbmciLCJ1cmwiLCJESVJFQ1RfVVJMIiwiSldUX1NFQ1JFVCIsIm1pbiIsInJlZmluZSIsInNlY3JldCIsImhhc1VwcGVyQ2FzZSIsInRlc3QiLCJoYXNMb3dlckNhc2UiLCJoYXNOdW1iZXJzIiwiaGFzU3BlY2lhbENoYXJzIiwiSldUX0VYUElSRVNfSU4iLCJkZWZhdWx0IiwiTk9ERV9FTlYiLCJlbnVtIiwiTkVYVF9QVUJMSUNfQVBQX1VSTCIsIm9wdGlvbmFsIiwiUE9SVCIsInJlZ2V4IiwidHJhbnNmb3JtIiwiTnVtYmVyIiwiU01UUF9IT1NUIiwiU01UUF9QT1JUIiwiU01UUF9VU0VSIiwiZW1haWwiLCJTTVRQX1BBU1NXT1JEIiwiU01UUF9TRUNVUkUiLCJ2YWwiLCJUUk9OX05FVFdPUksiLCJUUk9OX0FQSV9LRVkiLCJVU0RUX0NPTlRSQUNUX0FERFJFU1MiLCJNQVhfRklMRV9TSVpFIiwiVVBMT0FEX0RJUiIsIkJDUllQVF9ST1VORFMiLCJTRVNTSU9OX1RJTUVPVVQiLCJSQVRFX0xJTUlUX1dJTkRPV19NUyIsIlJBVEVfTElNSVRfTUFYX1JFUVVFU1RTIiwiQ09JTkdFQ0tPX0FQSV9VUkwiLCJMT0dfTEVWRUwiLCJFTkFCTEVfUkVRVUVTVF9MT0dHSU5HIiwiRU5BQkxFX1JFR0lTVFJBVElPTiIsIkVOQUJMRV9LWUMiLCJFTkFCTEVfV0lUSERSQVdBTFMiLCJNQUlOVEVOQU5DRV9NT0RFIiwiZW52U2NoZW1hV2l0aENvbmRpdGlvbmFscyIsImRhdGEiLCJzbXRwRmllbGRzIiwiaGFzQW55U210cCIsInNvbWUiLCJmaWVsZCIsInVuZGVmaW5lZCIsImhhc0FsbFNtdHAiLCJldmVyeSIsIm1lc3NhZ2UiLCJ2YWxpZGF0ZUVudmlyb25tZW50IiwicmVzdWx0Iiwic2FmZVBhcnNlIiwicHJvY2VzcyIsImVudiIsInN1Y2Nlc3MiLCJlcnJvcnMiLCJlcnJvciIsIm1hcCIsImVyciIsInBhdGgiLCJqb2luIiwidmFsaWRhdGVkRW52IiwiZ2V0VmFsaWRhdGVkRW52IiwidmFsaWRhdGlvbiIsImNvbnNvbGUiLCJmb3JFYWNoIiwiZXhpdCIsImxvZyIsImNvbmZpZyIsImlzRGV2ZWxvcG1lbnQiLCJpc1Byb2R1Y3Rpb24iLCJpc1Rlc3QiLCJkYXRhYmFzZSIsImRpcmVjdFVybCIsImp3dCIsImV4cGlyZXNJbiIsInNlcnZlciIsInBvcnQiLCJhcHBVcmwiLCJpc0NvbmZpZ3VyZWQiLCJob3N0IiwidXNlciIsInBhc3N3b3JkIiwic2VjdXJlIiwidHJvbiIsIm5ldHdvcmsiLCJhcGlLZXkiLCJ1c2R0Q29udHJhY3QiLCJzZWN1cml0eSIsImJjcnlwdFJvdW5kcyIsInNlc3Npb25UaW1lb3V0IiwibWF4RmlsZVNpemUiLCJ1cGxvYWREaXIiLCJyYXRlTGltaXQiLCJ3aW5kb3dNcyIsIm1heFJlcXVlc3RzIiwiZmVhdHVyZXMiLCJyZWdpc3RyYXRpb25FbmFibGVkIiwia3ljRW5hYmxlZCIsIndpdGhkcmF3YWxzRW5hYmxlZCIsIm1haW50ZW5hbmNlTW9kZSIsImxvZ2dpbmciLCJsZXZlbCIsInJlcXVlc3RMb2dnaW5nIiwiZXh0ZXJuYWwiLCJjb2luZ2Vja29BcGlVcmwiLCJpc0J1aWxkaW5nIiwiTkVYVF9QSEFTRSIsImNoZWNrQ3JpdGljYWxFbnZWYXJzIiwiY3JpdGljYWwiLCJtaXNzaW5nIiwia2V5IiwicHVzaCIsInZhbGlkIiwibGVuZ3RoIiwiZ2VuZXJhdGVTdHJvbmdKV1RTZWNyZXQiLCJjaGFycyIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImkiLCJzcGxpdCIsInNvcnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/envValidation.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/errorLogger.ts":
/*!********************************!*\
  !*** ./src/lib/errorLogger.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorLogger: () => (/* binding */ ErrorLogger),\n/* harmony export */   setupGlobalErrorHandlers: () => (/* binding */ setupGlobalErrorHandlers),\n/* harmony export */   withErrorLogging: () => (/* binding */ withErrorLogging)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\nclass ErrorLogger {\n    /**\n   * Log server-side errors with comprehensive details\n   */ static async logError(data) {\n        try {\n            const error = data.error instanceof Error ? data.error : new Error(String(data.error));\n            const logData = {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: {\n                    message: error.message,\n                    stack: error.stack,\n                    name: error.name,\n                    requestUrl: data.requestUrl,\n                    requestMethod: data.requestMethod,\n                    requestBody: data.requestBody,\n                    additionalData: data.additionalData,\n                    timestamp: new Date().toISOString()\n                },\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            };\n            await _database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create(logData);\n            // Also log to console for development\n            console.error(`[${data.action}] Error logged:`, {\n                message: error.message,\n                stack: error.stack,\n                userId: data.userId,\n                adminId: data.adminId,\n                url: data.requestUrl\n            });\n        } catch (logError) {\n            // Fallback to console if database logging fails\n            console.error('Failed to log error to database:', logError);\n            console.error('Original error:', data.error);\n        }\n    }\n    /**\n   * Log API route errors with request context\n   */ static async logApiError(request, error, action, userId, adminId, additionalData) {\n        try {\n            let requestBody = null;\n            // Safely extract request body\n            try {\n                if (request.method !== 'GET' && request.headers.get('content-type')?.includes('application/json')) {\n                    const clonedRequest = request.clone();\n                    requestBody = await clonedRequest.json();\n                    // Remove sensitive data from logs\n                    if (requestBody.password) requestBody.password = '[REDACTED]';\n                    if (requestBody.token) requestBody.token = '[REDACTED]';\n                    if (requestBody.apiKey) requestBody.apiKey = '[REDACTED]';\n                }\n            } catch (bodyError) {\n            // Ignore body parsing errors\n            }\n            await this.logError({\n                action,\n                error,\n                userId,\n                adminId,\n                ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n                userAgent: request.headers.get('user-agent') || 'unknown',\n                requestUrl: request.url,\n                requestMethod: request.method,\n                requestBody,\n                additionalData\n            });\n        } catch (logError) {\n            console.error('Failed to log API error:', logError);\n            console.error('Original error:', error);\n        }\n    }\n    /**\n   * Log client-side errors received from frontend\n   */ static async logClientError(data) {\n        try {\n            await _database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'CLIENT_ERROR',\n                userId: data.userId,\n                details: {\n                    message: data.message,\n                    stack: data.stack,\n                    url: data.url,\n                    timestamp: data.timestamp,\n                    additionalData: data.additionalData\n                },\n                userAgent: data.userAgent\n            });\n        } catch (logError) {\n            console.error('Failed to log client error:', logError);\n        }\n    }\n    /**\n   * Log authentication errors\n   */ static async logAuthError(request, error, email, additionalData) {\n        await this.logApiError(request, error, 'AUTH_ERROR', undefined, undefined, {\n            email,\n            ...additionalData\n        });\n    }\n    /**\n   * Log database errors\n   */ static async logDatabaseError(error, operation, table, userId, additionalData) {\n        try {\n            await _database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'DATABASE_ERROR',\n                userId,\n                details: {\n                    message: error instanceof Error ? error.message : String(error),\n                    stack: error instanceof Error ? error.stack : undefined,\n                    operation,\n                    table,\n                    timestamp: new Date().toISOString(),\n                    additionalData\n                }\n            });\n        } catch (logError) {\n            console.error('Failed to log database error:', logError);\n            console.error('Original error:', error);\n        }\n    }\n    /**\n   * Log business logic errors\n   */ static async logBusinessError(error, operation, userId, adminId, additionalData) {\n        await this.logError({\n            action: 'BUSINESS_LOGIC_ERROR',\n            error,\n            userId,\n            adminId,\n            additionalData: {\n                operation,\n                ...additionalData\n            }\n        });\n    }\n    /**\n   * Log external API errors (Tron, payment gateways, etc.)\n   */ static async logExternalApiError(error, service, endpoint, userId, additionalData) {\n        await this.logError({\n            action: 'EXTERNAL_API_ERROR',\n            error,\n            userId,\n            additionalData: {\n                service,\n                endpoint,\n                ...additionalData\n            }\n        });\n    }\n    /**\n   * Log validation errors\n   */ static async logValidationError(error, field, value, userId, additionalData) {\n        await this.logError({\n            action: 'VALIDATION_ERROR',\n            error,\n            userId,\n            additionalData: {\n                field,\n                value,\n                ...additionalData\n            }\n        });\n    }\n}\n/**\n * Middleware function to wrap API routes with error logging\n */ function withErrorLogging(handler, actionName) {\n    return async (...args)=>{\n        try {\n            return await handler(...args);\n        } catch (error) {\n            // Extract request if it's the first argument\n            const request = args[0];\n            if (request && typeof request === 'object' && 'url' in request) {\n                await ErrorLogger.logApiError(request, error, actionName);\n            } else {\n                await ErrorLogger.logError({\n                    action: actionName,\n                    error: error\n                });\n            }\n            throw error; // Re-throw to maintain original behavior\n        }\n    };\n}\n/**\n * Global error handler for unhandled promise rejections\n */ function setupGlobalErrorHandlers() {\n    if (typeof process !== 'undefined') {\n        process.on('unhandledRejection', async (reason, promise)=>{\n            console.error('Unhandled Rejection at:', promise, 'reason:', reason);\n            await ErrorLogger.logError({\n                action: 'UNHANDLED_REJECTION',\n                error: reason instanceof Error ? reason : new Error(String(reason)),\n                additionalData: {\n                    promise: promise.toString()\n                }\n            });\n        });\n        process.on('uncaughtException', async (error)=>{\n            console.error('Uncaught Exception:', error);\n            await ErrorLogger.logError({\n                action: 'UNCAUGHT_EXCEPTION',\n                error\n            });\n            // Exit process after logging\n            process.exit(1);\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/errorLogger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&page=%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Femail-templates%2Fseed%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();