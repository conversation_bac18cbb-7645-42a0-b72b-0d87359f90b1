"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/base32.js";
exports.ids = ["vendor-chunks/base32.js"];
exports.modules = {

/***/ "(rsc)/./node_modules/base32.js/base32.js":
/*!******************************************!*\
  !*** ./node_modules/base32.js/base32.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n/**\n * Generate a character map.\n * @param {string} alphabet e.g. \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\"\n * @param {object} mappings map overrides from key to value\n * @method\n */\n\nvar charmap = function (alphabet, mappings) {\n  mappings || (mappings = {});\n  alphabet.split(\"\").forEach(function (c, i) {\n    if (!(c in mappings)) mappings[c] = i;\n  });\n  return mappings;\n}\n\n/**\n * The RFC 4648 base 32 alphabet and character map.\n * @see {@link https://tools.ietf.org/html/rfc4648}\n */\n\nvar rfc4648 = {\n  alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\",\n  charmap: {\n    0: 14,\n    1: 8\n  }\n};\n\nrfc4648.charmap = charmap(rfc4648.alphabet, rfc4648.charmap);\n\n/**\n * The Crockford base 32 alphabet and character map.\n * @see {@link http://www.crockford.com/wrmg/base32.html}\n */\n\nvar crockford = {\n  alphabet: \"0123456789ABCDEFGHJKMNPQRSTVWXYZ\",\n  charmap: {\n    O: 0,\n    I: 1,\n    L: 1\n  }\n};\n\ncrockford.charmap = charmap(crockford.alphabet, crockford.charmap);\n\n/**\n * Create a new `Decoder` with the given options.\n *\n * @param {object} [options]\n *   @param {string} [type] Supported Base-32 variants are \"rfc4648\" and\n *     \"crockford\".\n *   @param {object} [charmap] Override the character map used in decoding.\n */\n\nfunction Decoder (options) {\n  this.buf = [];\n  this.shift = 8;\n  this.carry = 0;\n\n  if (options) {\n\n    switch (options.type) {\n      case \"rfc4648\":\n        this.charmap = exports.rfc4648.charmap;\n        break;\n      case \"crockford\":\n        this.charmap = exports.crockford.charmap;\n        break;\n      default:\n        throw new Error(\"invalid type\");\n    }\n\n    if (options.charmap) this.charmap = options.charmap;\n  }\n}\n\n/**\n * The default character map coresponds to RFC4648.\n */\n\nDecoder.prototype.charmap = rfc4648.charmap;\n\n/**\n * Decode a string, continuing from the previous state.\n *\n * @param {string} str\n * @return {Decoder} this\n */\n\nDecoder.prototype.write = function (str) {\n  var charmap = this.charmap;\n  var buf = this.buf;\n  var shift = this.shift;\n  var carry = this.carry;\n\n  // decode string\n  str.toUpperCase().split(\"\").forEach(function (char) {\n\n    // ignore padding\n    if (char == \"=\") return;\n\n    // lookup symbol\n    var symbol = charmap[char] & 0xff;\n\n    // 1: 00000 000\n    // 2:          00 00000 0\n    // 3:                    0000 0000\n    // 4:                             0 00000 00\n    // 5:                                       000 00000\n    // 6:                                                00000 000\n    // 7:                                                         00 00000 0\n\n    shift -= 5;\n    if (shift > 0) {\n      carry |= symbol << shift;\n    } else if (shift < 0) {\n      buf.push(carry | (symbol >> -shift));\n      shift += 8;\n      carry = (symbol << shift) & 0xff;\n    } else {\n      buf.push(carry | symbol);\n      shift = 8;\n      carry = 0;\n    }\n  });\n\n  // save state\n  this.shift = shift;\n  this.carry = carry;\n\n  // for chaining\n  return this;\n};\n\n/**\n * Finish decoding.\n *\n * @param {string} [str] The final string to decode.\n * @return {Array} Decoded byte array.\n */\n\nDecoder.prototype.finalize = function (str) {\n  if (str) {\n    this.write(str);\n  }\n  if (this.shift !== 8 && this.carry !== 0) {\n    this.buf.push(this.carry);\n    this.shift = 8;\n    this.carry = 0;\n  }\n  return this.buf;\n};\n\n/**\n * Create a new `Encoder` with the given options.\n *\n * @param {object} [options]\n *   @param {string} [type] Supported Base-32 variants are \"rfc4648\" and\n *     \"crockford\".\n *   @param {object} [alphabet] Override the alphabet used in encoding.\n */\n\nfunction Encoder (options) {\n  this.buf = \"\";\n  this.shift = 3;\n  this.carry = 0;\n\n  if (options) {\n\n    switch (options.type) {\n      case \"rfc4648\":\n        this.alphabet = exports.rfc4648.alphabet;\n        break;\n      case \"crockford\":\n        this.alphabet = exports.crockford.alphabet;\n        break;\n      default:\n        throw new Error(\"invalid type\");\n    }\n\n    if (options.alphabet) this.alphabet = options.alphabet;\n    else if (options.lc) this.alphabet = this.alphabet.toLowerCase();\n  }\n}\n\n/**\n * The default alphabet coresponds to RFC4648.\n */\n\nEncoder.prototype.alphabet = rfc4648.alphabet;\n\n/**\n * Encode a byte array, continuing from the previous state.\n *\n * @param {byte[]} buf The byte array to encode.\n * @return {Encoder} this\n */\n\nEncoder.prototype.write = function (buf) {\n  var shift = this.shift;\n  var carry = this.carry;\n  var symbol;\n  var byte;\n  var i;\n\n  // encode each byte in buf\n  for (i = 0; i < buf.length; i++) {\n    byte = buf[i];\n\n    // 1: 00000 000\n    // 2:          00 00000 0\n    // 3:                    0000 0000\n    // 4:                             0 00000 00\n    // 5:                                       000 00000\n    // 6:                                                00000 000\n    // 7:                                                         00 00000 0\n\n    symbol = carry | (byte >> shift);\n    this.buf += this.alphabet[symbol & 0x1f];\n\n    if (shift > 5) {\n      shift -= 5;\n      symbol = byte >> shift;\n      this.buf += this.alphabet[symbol & 0x1f];\n    }\n\n    shift = 5 - shift;\n    carry = byte << shift;\n    shift = 8 - shift;\n  }\n\n  // save state\n  this.shift = shift;\n  this.carry = carry;\n\n  // for chaining\n  return this;\n};\n\n/**\n * Finish encoding.\n *\n * @param {byte[]} [buf] The final byte array to encode.\n * @return {string} The encoded byte array.\n */\n\nEncoder.prototype.finalize = function (buf) {\n  if (buf) {\n    this.write(buf);\n  }\n  if (this.shift !== 3) {\n    this.buf += this.alphabet[this.carry & 0x1f];\n    this.shift = 3;\n    this.carry = 0;\n  }\n  return this.buf;\n};\n\n/**\n * Convenience encoder.\n *\n * @param {byte[]} buf The byte array to encode.\n * @param {object} [options] Options to pass to the encoder.\n * @return {string} The encoded string.\n */\n\nexports.encode = function (buf, options) {\n  return new Encoder(options).finalize(buf);\n};\n\n/**\n * Convenience decoder.\n *\n * @param {string} str The string to decode.\n * @param {object} [options] Options to pass to the decoder.\n * @return {byte[]} The decoded byte array.\n */\n\nexports.decode = function (str, options) {\n  return new Decoder(options).finalize(str);\n};\n\n// Exports.\nexports.Decoder = Decoder;\nexports.Encoder = Encoder;\nexports.charmap = charmap;\nexports.crockford = crockford;\nexports.rfc4648 = rfc4648;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/base32.js/base32.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/base32.js/index.js":
/*!*****************************************!*\
  !*** ./node_modules/base32.js/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Module dependencies.\nvar base32 = __webpack_require__(/*! ./base32 */ \"(rsc)/./node_modules/base32.js/base32.js\");\n\n\n// Wrap decoder finalize to return a buffer;\nvar finalizeDecode = base32.Decoder.prototype.finalize;\nbase32.Decoder.prototype.finalize = function (buf) {\n  var bytes = finalizeDecode.call(this, buf);\n  return new Buffer(bytes);\n};\n\n\n// Export Base32.\nmodule.exports = base32;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYmFzZTMyLmpzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0EsYUFBYSxtQkFBTyxDQUFDLDBEQUFVOzs7QUFHL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRyZWFtXFxEZXNrdG9wXFxIYXNoX01pbmluZ3NcXGhhc2hjb3JleFxcbm9kZV9tb2R1bGVzXFxiYXNlMzIuanNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG4vLyBNb2R1bGUgZGVwZW5kZW5jaWVzLlxudmFyIGJhc2UzMiA9IHJlcXVpcmUoXCIuL2Jhc2UzMlwiKTtcblxuXG4vLyBXcmFwIGRlY29kZXIgZmluYWxpemUgdG8gcmV0dXJuIGEgYnVmZmVyO1xudmFyIGZpbmFsaXplRGVjb2RlID0gYmFzZTMyLkRlY29kZXIucHJvdG90eXBlLmZpbmFsaXplO1xuYmFzZTMyLkRlY29kZXIucHJvdG90eXBlLmZpbmFsaXplID0gZnVuY3Rpb24gKGJ1Zikge1xuICB2YXIgYnl0ZXMgPSBmaW5hbGl6ZURlY29kZS5jYWxsKHRoaXMsIGJ1Zik7XG4gIHJldHVybiBuZXcgQnVmZmVyKGJ5dGVzKTtcbn07XG5cblxuLy8gRXhwb3J0IEJhc2UzMi5cbm1vZHVsZS5leHBvcnRzID0gYmFzZTMyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/base32.js/index.js\n");

/***/ })

};
;