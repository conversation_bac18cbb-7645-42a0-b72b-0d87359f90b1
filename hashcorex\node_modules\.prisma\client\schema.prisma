// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  firstName         String
  lastName          String
  password          String
  referralId        String    @unique
  referrerId        String? // Sponsor ID (who referred this user for commissions)
  role              UserRole  @default(USER)
  isActive          Boolean   @default(true)
  kycStatus         KYCStatus @default(PENDING)
  leftReferralId    String?
  rightReferralId   String?
  withdrawalAddress String? // Default USDT TRC20 address for withdrawals
  profilePicture    String? // Profile picture file path

  // Enhanced tracking fields for performance
  directReferralCount        Int       @default(0) // Cache of direct referrals count
  totalLeftDownline          Int       @default(0) // Cache of left side total downline
  totalRightDownline         Int       @default(0) // Cache of right side total downline
  lastTreeUpdate             DateTime? // Last time tree counts were updated
  hasReceivedFirstCommission Boolean   @default(false) // Track if sponsor got first commission from this user

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  miningUnits         MiningUnit[]
  transactions        Transaction[]
  kycDocuments        KYCDocument[]
  withdrawalRequests  WithdrawalRequest[]
  referrals           Referral[]           @relation("ReferrerRelation")
  referredBy          Referral[]           @relation("ReferredRelation")
  binaryPoints        BinaryPoints?
  systemLogs          SystemLog[]
  depositAddress      DepositAddress?
  walletBalance       WalletBalance?
  depositTransactions DepositTransaction[]
  supportTickets      SupportTicket[]
  ticketResponses     TicketResponse[]

  // Self-referential relation for sponsor tracking
  sponsor        User?  @relation("SponsorRelation", fields: [referrerId], references: [id])
  sponsoredUsers User[] @relation("SponsorRelation")

  // Security and performance indexes
  @@index([email])
  @@index([referralId])
  @@index([referrerId])
  @@index([leftReferralId])
  @@index([rightReferralId])
  @@index([kycStatus])
  @@index([role])
  @@index([isActive])
  @@index([createdAt])
  @@index([referrerId, isActive]) // Composite index for active referrals
  @@index([kycStatus, isActive]) // Composite index for active KYC users
  @@map("users")
}

model MiningUnit {
  id               String           @id @default(cuid())
  userId           String
  thsAmount        Float
  investmentAmount Float
  startDate        DateTime         @default(now())
  expiryDate       DateTime
  dailyROI         Float
  totalEarned      Float            @default(0) // Legacy field - will be calculated from earnings breakdown
  miningEarnings   Float            @default(0) // Earnings from daily mining ROI
  referralEarnings Float            @default(0) // Earnings from direct referral commissions allocated to this unit
  binaryEarnings   Float            @default(0) // Earnings from binary bonuses allocated to this unit
  status           MiningUnitStatus @default(ACTIVE)
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  // Relations
  user                User                           @relation(fields: [userId], references: [id], onDelete: Cascade)
  earningsAllocations MiningUnitEarningsAllocation[]

  // Performance and security indexes
  @@index([userId])
  @@index([status])
  @@index([expiryDate])
  @@index([createdAt])
  @@index([userId, status]) // Composite index for user's active mining units
  @@index([status, expiryDate]) // Composite index for active units expiry check
  @@index([userId, createdAt]) // Composite index for user's mining history
  @@map("mining_units")
}

model MiningUnitEarningsAllocation {
  id            String      @id @default(cuid())
  miningUnitId  String
  transactionId String // Reference to the original transaction
  earningType   EarningType
  amount        Float
  allocatedAt   DateTime    @default(now())
  description   String

  // Relations
  miningUnit  MiningUnit  @relation(fields: [miningUnitId], references: [id], onDelete: Cascade)
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@map("mining_unit_earnings_allocations")
}

model Transaction {
  id          String            @id @default(cuid())
  userId      String
  type        TransactionType
  amount      Float
  description String
  status      TransactionStatus @default(PENDING)
  reference   String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  user                User                           @relation(fields: [userId], references: [id], onDelete: Cascade)
  earningsAllocations MiningUnitEarningsAllocation[]

  // Performance and security indexes
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
  @@index([reference])
  @@index([userId, type]) // Composite index for user's transaction types
  @@index([userId, createdAt]) // Composite index for user's transaction history
  @@index([type, status]) // Composite index for transaction processing
  @@map("transactions")
}

model Referral {
  id               String        @id @default(cuid())
  referrerId       String // Binary tree parent (where user is placed)
  referredId       String // The referred user
  placementSide    PlacementSide
  commissionEarned Float         @default(0)
  isDirectSponsor  Boolean       @default(false) // True if referrer is also the sponsor
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt

  // Relations
  referrer User @relation("ReferrerRelation", fields: [referrerId], references: [id], onDelete: Cascade)
  referred User @relation("ReferredRelation", fields: [referredId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@unique([referrerId, referredId])
  @@index([referrerId, placementSide])
  @@index([referredId])
  @@index([createdAt])
  @@index([isDirectSponsor])
  @@map("referrals")
}

model BinaryPoints {
  id            String    @id @default(cuid())
  userId        String    @unique
  leftPoints    Float     @default(0)
  rightPoints   Float     @default(0)
  matchedPoints Float     @default(0)
  totalMatched  Float     @default(0) // Lifetime total matched points
  flushDate     DateTime?
  lastMatchDate DateTime? // Last time points were matched
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([leftPoints])
  @@index([rightPoints])
  @@index([flushDate])
  @@index([lastMatchDate])
  @@map("binary_points")
}

model KYCDocument {
  id              String        @id @default(cuid())
  userId          String
  documentType    DocumentType
  idType          IDType? // Type of ID document (National ID, Passport, Driving License)
  documentSide    DocumentSide? // Front, Back (not applicable for selfie and passport)
  filePath        String
  status          KYCStatus     @default(PENDING)
  reviewedAt      DateTime?
  reviewedBy      String?
  rejectionReason String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc_documents")
}

model WithdrawalRequest {
  id              String           @id @default(cuid())
  userId          String
  amount          Float
  usdtAddress     String
  status          WithdrawalStatus @default(PENDING)
  txid            String?
  processedBy     String?
  processedAt     DateTime?
  rejectionReason String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("withdrawal_requests")
}

model AdminSettings {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admin_settings")
}

model SystemLog {
  id        String   @id @default(cuid())
  action    String
  userId    String?
  adminId   String?
  details   String?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Security and monitoring indexes
  @@index([action])
  @@index([userId])
  @@index([adminId])
  @@index([createdAt])
  @@index([ipAddress])
  @@index([action, createdAt]) // Composite index for action timeline
  @@index([userId, action]) // Composite index for user activity
  @@index([ipAddress, createdAt]) // Composite index for IP activity tracking
  @@map("system_logs")
}

model DepositAddress {
  id        String   @id @default(cuid())
  userId    String   @unique
  address   String   @unique
  network   String // TRC20, ERC20, etc.
  currency  String // USDT, USDC, etc.
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("deposit_addresses")
}

model WalletBalance {
  id               String   @id @default(cuid())
  userId           String   @unique
  availableBalance Float    @default(0)
  pendingBalance   Float    @default(0)
  totalDeposits    Float    @default(0)
  totalWithdrawals Float    @default(0)
  totalEarnings    Float    @default(0)
  lastUpdated      DateTime @default(now())
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("wallet_balances")
}

model DepositTransaction {
  id             String        @id @default(cuid())
  userId         String
  transactionId  String        @unique // Tron transaction ID
  amount         Float
  usdtAmount     Float // Actual USDT amount from TRC20 transfer
  tronAddress    String // Deposit address used
  senderAddress  String? // Address that sent the USDT
  status         DepositStatus @default(PENDING)
  blockNumber    String?
  blockTimestamp DateTime?
  confirmations  Int           @default(0)
  verifiedAt     DateTime?
  processedAt    DateTime?
  failureReason  String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([transactionId])
  @@index([status])
  @@index([createdAt])
  @@map("deposit_transactions")
}

model SupportTicket {
  id        String         @id @default(cuid())
  userId    String
  subject   String
  message   String         @db.Text
  status    TicketStatus   @default(OPEN)
  priority  TicketPriority @default(MEDIUM)
  category  String? // Optional category like "Technical", "Billing", etc.
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt

  // Relations
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  responses TicketResponse[]

  @@index([userId])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@map("support_tickets")
}

model TicketResponse {
  id        String   @id @default(cuid())
  ticketId  String
  userId    String? // User who created the response (null for admin responses)
  message   String   @db.Text
  isAdmin   Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relations
  ticket SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  user   User?         @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([ticketId])
  @@index([createdAt])
  @@map("ticket_responses")
}

model EmailTemplate {
  id          String   @id @default(cuid())
  name        String   @unique // e.g., 'otp_verification', 'welcome_email'
  subject     String
  htmlContent String   @db.Text
  textContent String?  @db.Text
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("email_templates")
}

model EmailLog {
  id        String      @id @default(cuid())
  to        String
  subject   String
  template  String?
  status    EmailStatus @default(PENDING)
  error     String?
  sentAt    DateTime?
  createdAt DateTime    @default(now())

  @@index([status])
  @@index([createdAt])
  @@map("email_logs")
}

model OTPVerification {
  id        String   @id @default(cuid())
  email     String
  otp       String
  purpose   String // 'email_verification', 'password_reset', etc.
  expiresAt DateTime
  verified  Boolean  @default(false)
  createdAt DateTime @default(now())

  @@index([email, purpose])
  @@index([expiresAt])
  @@map("otp_verifications")
}

// Enums
enum UserRole {
  USER
  ADMIN
}

enum KYCStatus {
  PENDING
  APPROVED
  REJECTED
}

enum MiningUnitStatus {
  ACTIVE
  EXPIRED
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  PURCHASE
  MINING_EARNINGS
  DIRECT_REFERRAL
  BINARY_BONUS
  ADMIN_CREDIT
  ADMIN_DEBIT
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
}

enum EarningType {
  MINING_EARNINGS
  DIRECT_REFERRAL
  BINARY_BONUS
}

enum PlacementSide {
  LEFT
  RIGHT
}

enum DocumentType {
  ID_DOCUMENT
  SELFIE
}

enum IDType {
  NATIONAL_ID
  PASSPORT
  DRIVING_LICENSE
}

enum DocumentSide {
  FRONT
  BACK
}

enum WithdrawalStatus {
  PENDING
  APPROVED
  COMPLETED
  REJECTED
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum EmailStatus {
  PENDING
  SENT
  FAILED
  BOUNCED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum DepositStatus {
  PENDING_VERIFICATION
  PENDING
  VERIFYING
  WAITING_FOR_CONFIRMATIONS
  CONFIRMED
  COMPLETED
  FAILED
  REJECTED
}
