"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/speakeasy";
exports.ids = ["vendor-chunks/speakeasy"];
exports.modules = {

/***/ "(rsc)/./node_modules/speakeasy/index.js":
/*!*****************************************!*\
  !*** ./node_modules/speakeasy/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar base32 = __webpack_require__(/*! base32.js */ \"(rsc)/./node_modules/base32.js/index.js\");\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar url = __webpack_require__(/*! url */ \"url\");\nvar util = __webpack_require__(/*! util */ \"util\");\n\n/**\n * Digest the one-time passcode options.\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {Integer} options.counter Counter value\n * @param {String} [options.encoding=\"ascii\"] Key encoding (ascii, hex,\n *   base32, base64).\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @param {String} [options.key] (DEPRECATED. Use `secret` instead.)\n *   Shared secret key\n * @return {Buffer} The one-time passcode as a buffer.\n */\n\nexports.digest = function digest (options) {\n  var i;\n\n  // unpack options\n  var secret = options.secret;\n  var counter = options.counter;\n  var encoding = options.encoding || 'ascii';\n  var algorithm = (options.algorithm || 'sha1').toLowerCase();\n\n  // Backwards compatibility - deprecated\n  if (options.key != null) {\n    console.warn('Speakeasy - Deprecation Notice - Specifying the secret using `key` is no longer supported. Use `secret` instead.');\n    secret = options.key;\n  }\n\n  // convert secret to buffer\n  if (!Buffer.isBuffer(secret)) {\n    secret = encoding === 'base32' ? base32.decode(secret)\n      : new Buffer(secret, encoding);\n  }\n\n  // create an buffer from the counter\n  var buf = new Buffer(8);\n  var tmp = counter;\n  for (i = 0; i < 8; i++) {\n    // mask 0xff over number to get last 8\n    buf[7 - i] = tmp & 0xff;\n\n    // shift 8 and get ready to loop over the next batch of 8\n    tmp = tmp >> 8;\n  }\n\n  // init hmac with the key\n  var hmac = crypto.createHmac(algorithm, secret);\n\n  // update hmac with the counter\n  hmac.update(buf);\n\n  // return the digest\n  return hmac.digest();\n};\n\n/**\n * Generate a counter-based one-time token. Specify the key and counter, and\n * receive the one-time password for that counter position as a string. You can\n * also specify a token length, as well as the encoding (ASCII, hexadecimal, or\n * base32) and the hashing algorithm to use (SHA1, SHA256, SHA512).\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {Integer} options.counter Counter value\n * @param {Buffer} [options.digest] Digest, automatically generated by default\n * @param {Integer} [options.digits=6] The number of digits for the one-time\n *   passcode.\n * @param {String} [options.encoding=\"ascii\"] Key encoding (ascii, hex,\n *   base32, base64).\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @param {String} [options.key] (DEPRECATED. Use `secret` instead.)\n *   Shared secret key\n * @param {Integer} [options.length=6] (DEPRECATED. Use `digits` instead.) The\n *   number of digits for the one-time passcode.\n * @return {String} The one-time passcode.\n */\n\nexports.hotp = function hotpGenerate (options) {\n  // unpack digits\n  // backward compatibility: `length` is also accepted here, but deprecated\n  var digits = (options.digits != null ? options.digits : options.length) || 6;\n  if (options.length != null) console.warn('Speakeasy - Deprecation Notice - Specifying token digits using `length` is no longer supported. Use `digits` instead.');\n\n  // digest the options\n  var digest = options.digest || exports.digest(options);\n\n  // compute HOTP offset\n  var offset = digest[digest.length - 1] & 0xf;\n\n  // calculate binary code (RFC4226 5.4)\n  var code = (digest[offset] & 0x7f) << 24 |\n    (digest[offset + 1] & 0xff) << 16 |\n    (digest[offset + 2] & 0xff) << 8 |\n    (digest[offset + 3] & 0xff);\n\n  // left-pad code\n  code = new Array(digits + 1).join('0') + code.toString(10);\n\n  // return length number off digits\n  return code.substr(-digits);\n};\n\n// Alias counter() for hotp()\nexports.counter = exports.hotp;\n\n/**\n * Verify a counter-based one-time token against the secret and return the delta.\n * By default, it verifies the token at the given counter value, with no leeway\n * (no look-ahead or look-behind). A token validated at the current counter value\n * will have a delta of 0.\n *\n * You can specify a window to add more leeway to the verification process.\n * Setting the window param will check for the token at the given counter value\n * as well as `window` tokens ahead (one-sided window). See param for more info.\n *\n * `verifyDelta()` will return the delta between the counter value of the token\n * and the given counter value. For example, if given a counter 5 and a window\n * 10, `verifyDelta()` will look at tokens from 5 to 15, inclusive. If it finds\n * it at counter position 7, it will return `{ delta: 2 }`.\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {String} options.token Passcode to validate\n * @param {Integer} options.counter Counter value. This should be stored by\n *   the application and must be incremented for each request.\n * @param {Integer} [options.digits=6] The number of digits for the one-time\n *   passcode.\n * @param {Integer} [options.window=0] The allowable margin for the counter.\n *   The function will check \"W\" codes in the future against the provided\n *   passcode, e.g. if W = 10, and C = 5, this function will check the\n *   passcode against all One Time Passcodes between 5 and 15, inclusive.\n * @param {String} [options.encoding=\"ascii\"] Key encoding (ascii, hex,\n *   base32, base64).\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @return {Object} On success, returns an object with the counter\n *   difference between the client and the server as the `delta` property (i.e.\n *   `{ delta: 0 }`).\n * @method hotp․verifyDelta\n * @global\n */\n\nexports.hotp.verifyDelta = function hotpVerifyDelta (options) {\n  var i;\n\n  // shadow options\n  options = Object.create(options);\n\n  // unpack options\n  var token = String(options.token);\n  var digits = parseInt(options.digits, 10) || 6;\n  var window = parseInt(options.window, 10) || 0;\n  var counter = parseInt(options.counter, 10) || 0;\n\n  // fail if token is not of correct length\n  if (token.length !== digits) {\n    return;\n  }\n\n  // parse token to integer\n  token = parseInt(token, 10);\n\n  // fail if token is NA\n  if (isNaN(token)) {\n    return;\n  }\n\n  // loop from C to C + W inclusive\n  for (i = counter; i <= counter + window; ++i) {\n    options.counter = i;\n    // domain-specific constant-time comparison for integer codes\n    if (parseInt(exports.hotp(options), 10) === token) {\n      // found a matching code, return delta\n      return {delta: i - counter};\n    }\n  }\n\n  // no codes have matched\n};\n\n/**\n * Verify a counter-based one-time token against the secret and return true if\n * it verifies. Helper function for `hotp.verifyDelta()`` that returns a boolean\n * instead of an object. For more on how to use a window with this, see\n * {@link hotp.verifyDelta}.\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {String} options.token Passcode to validate\n * @param {Integer} options.counter Counter value. This should be stored by\n *   the application and must be incremented for each request.\n * @param {Integer} [options.digits=6] The number of digits for the one-time\n *   passcode.\n * @param {Integer} [options.window=0] The allowable margin for the counter.\n *   The function will check \"W\" codes in the future against the provided\n *   passcode, e.g. if W = 10, and C = 5, this function will check the\n *   passcode against all One Time Passcodes between 5 and 15, inclusive.\n * @param {String} [options.encoding=\"ascii\"] Key encoding (ascii, hex,\n *   base32, base64).\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @return {Boolean} Returns true if the token matches within the given\n *   window, false otherwise.\n * @method hotp․verify\n * @global\n */\nexports.hotp.verify = function hotpVerify (options) {\n  return exports.hotp.verifyDelta(options) != null;\n};\n\n/**\n * Calculate counter value based on given options. A counter value converts a\n * TOTP time into a counter value by finding the number of time steps that have\n * passed since the epoch to the current time.\n *\n * @param {Object} options\n * @param {Integer} [options.time] Time in seconds with which to calculate\n *   counter value. Defaults to `Date.now()`.\n * @param {Integer} [options.step=30] Time step in seconds\n * @param {Integer} [options.epoch=0] Initial time since the UNIX epoch from\n *   which to calculate the counter value. Defaults to 0 (no offset).\n * @param {Integer} [options.initial_time=0] (DEPRECATED. Use `epoch` instead.)\n *   Initial time in seconds since the UNIX epoch from which to calculate the\n *   counter value. Defaults to 0 (no offset).\n * @return {Integer} The calculated counter value.\n * @private\n */\n\nexports._counter = function _counter (options) {\n  var step = options.step || 30;\n  var time = options.time != null ? (options.time * 1000) : Date.now();\n\n  // also accepts 'initial_time', but deprecated\n  var epoch = (options.epoch != null ? (options.epoch * 1000) : (options.initial_time * 1000)) || 0;\n  if (options.initial_time != null) console.warn('Speakeasy - Deprecation Notice - Specifying the epoch using `initial_time` is no longer supported. Use `epoch` instead.');\n\n  return Math.floor((time - epoch) / step / 1000);\n};\n\n/**\n * Generate a time-based one-time token. Specify the key, and receive the\n * one-time password for that time as a string. By default, it uses the current\n * time and a time step of 30 seconds, so there is a new token every 30 seconds.\n * You may override the time step and epoch for custom timing. You can also\n * specify a token length, as well as the encoding (ASCII, hexadecimal, or\n * base32) and the hashing algorithm to use (SHA1, SHA256, SHA512).\n *\n * Under the hood, TOTP calculates the counter value by finding how many time\n * steps have passed since the epoch, and calls HOTP with that counter value.\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {Integer} [options.time] Time in seconds with which to calculate\n *   counter value. Defaults to `Date.now()`.\n * @param {Integer} [options.step=30] Time step in seconds\n * @param {Integer} [options.epoch=0] Initial time in seconds since the UNIX\n *   epoch from which to calculate the counter value. Defaults to 0 (no offset).\n * @param {Integer} [options.counter] Counter value, calculated by default.\n * @param {Integer} [options.digits=6] The number of digits for the one-time\n *   passcode.\n * @param {String} [options.encoding=\"ascii\"] Key encoding (ascii, hex,\n *   base32, base64).\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @param {String} [options.key] (DEPRECATED. Use `secret` instead.)\n *   Shared secret key\n * @param {Integer} [options.initial_time=0] (DEPRECATED. Use `epoch` instead.)\n *   Initial time in seconds since the UNIX epoch from which to calculate the\n *   counter value. Defaults to 0 (no offset).\n * @param {Integer} [options.length=6] (DEPRECATED. Use `digits` instead.) The\n *   number of digits for the one-time passcode.\n * @return {String} The one-time passcode.\n */\n\nexports.totp = function totpGenerate (options) {\n  // shadow options\n  options = Object.create(options);\n\n  // calculate default counter value\n  if (options.counter == null) options.counter = exports._counter(options);\n\n  // pass to hotp\n  return this.hotp(options);\n};\n\n// Alias time() for totp()\nexports.time = exports.totp;\n\n/**\n * Verify a time-based one-time token against the secret and return the delta.\n * By default, it verifies the token at the current time window, with no leeway\n * (no look-ahead or look-behind). A token validated at the current time window\n * will have a delta of 0.\n *\n * You can specify a window to add more leeway to the verification process.\n * Setting the window param will check for the token at the given counter value\n * as well as `window` tokens ahead and `window` tokens behind (two-sided\n * window). See param for more info.\n *\n * `verifyDelta()` will return the delta between the counter value of the token\n * and the given counter value. For example, if given a time at counter 1000 and\n * a window of 5, `verifyDelta()` will look at tokens from 995 to 1005,\n * inclusive. In other words, if the time-step is 30 seconds, it will look at\n * tokens from 2.5 minutes ago to 2.5 minutes in the future, inclusive.\n * If it finds it at counter position 1002, it will return `{ delta: 2 }`.\n * If it finds it at counter position 997, it will return `{ delta: -3 }`.\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {String} options.token Passcode to validate\n * @param {Integer} [options.time] Time in seconds with which to calculate\n *   counter value. Defaults to `Date.now()`.\n * @param {Integer} [options.step=30] Time step in seconds\n * @param {Integer} [options.epoch=0] Initial time in seconds since the UNIX\n *   epoch from which to calculate the counter value. Defaults to 0 (no offset).\n * @param {Integer} [options.counter] Counter value, calculated by default.\n * @param {Integer} [options.digits=6] The number of digits for the one-time\n *   passcode.\n * @param {Integer} [options.window=0] The allowable margin for the counter.\n *   The function will check \"W\" codes in the future and the past against the\n *   provided passcode, e.g. if W = 5, and C = 1000, this function will check\n *   the passcode against all One Time Passcodes between 995 and 1005,\n *   inclusive.\n * @param {String} [options.encoding=\"ascii\"] Key encoding (ascii, hex,\n *   base32, base64).\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @return {Object} On success, returns an object with the time step\n *   difference between the client and the server as the `delta` property (e.g.\n *   `{ delta: 0 }`).\n * @method totp․verifyDelta\n * @global\n */\n\nexports.totp.verifyDelta = function totpVerifyDelta (options) {\n  // shadow options\n  options = Object.create(options);\n\n  // unpack options\n  var window = parseInt(options.window, 10) || 0;\n\n  // calculate default counter value\n  if (options.counter == null) options.counter = exports._counter(options);\n\n  // adjust for two-sided window\n  options.counter -= window;\n  options.window += window;\n\n  // pass to hotp.verifyDelta\n  var delta = exports.hotp.verifyDelta(options);\n\n  // adjust for two-sided window\n  if (delta) {\n    delta.delta -= window;\n  }\n\n  return delta;\n};\n\n/**\n * Verify a time-based one-time token against the secret and return true if it\n * verifies. Helper function for verifyDelta() that returns a boolean instead of\n * an object. For more on how to use a window with this, see\n * {@link totp.verifyDelta}.\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {String} options.token Passcode to validate\n * @param {Integer} [options.time] Time in seconds with which to calculate\n *   counter value. Defaults to `Date.now()`.\n * @param {Integer} [options.step=30] Time step in seconds\n * @param {Integer} [options.epoch=0] Initial time in seconds  since the UNIX\n *   epoch from which to calculate the counter value. Defaults to 0 (no offset).\n * @param {Integer} [options.counter] Counter value, calculated by default.\n * @param {Integer} [options.digits=6] The number of digits for the one-time\n *   passcode.\n * @param {Integer} [options.window=0] The allowable margin for the counter.\n *   The function will check \"W\" codes in the future and the past against the\n *   provided passcode, e.g. if W = 5, and C = 1000, this function will check\n *   the passcode against all One Time Passcodes between 995 and 1005,\n *   inclusive.\n * @param {String} [options.encoding=\"ascii\"] Key encoding (ascii, hex,\n *   base32, base64).\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @return {Boolean} Returns true if the token matches within the given\n *   window, false otherwise.\n * @method totp․verify\n * @global\n */\nexports.totp.verify = function totpVerify (options) {\n  return exports.totp.verifyDelta(options) != null;\n};\n\n/**\n * @typedef GeneratedSecret\n * @type Object\n * @property {String} ascii ASCII representation of the secret\n * @property {String} hex Hex representation of the secret\n * @property {String} base32 Base32 representation of the secret\n * @property {String} qr_code_ascii URL for the QR code for the ASCII secret.\n * @property {String} qr_code_hex URL for the QR code for the hex secret.\n * @property {String} qr_code_base32 URL for the QR code for the base32 secret.\n * @property {String} google_auth_qr URL for the Google Authenticator otpauth\n *   URL's QR code.\n * @property {String} otpauth_url Google Authenticator-compatible otpauth URL.\n */\n\n/**\n * Generates a random secret with the set A-Z a-z 0-9 and symbols, of any length\n * (default 32). Returns the secret key in ASCII, hexadecimal, and base32 format,\n * along with the URL used for the QR code for Google Authenticator (an otpauth\n * URL). Use a QR code library to generate a QR code based on the Google\n * Authenticator URL to obtain a QR code you can scan into the app.\n *\n * @param {Object} options\n * @param {Integer} [options.length=32] Length of the secret\n * @param {Boolean} [options.symbols=false] Whether to include symbols\n * @param {Boolean} [options.otpauth_url=true] Whether to output a Google\n *   Authenticator-compatible otpauth:// URL (only returns otpauth:// URL, no\n *   QR code)\n * @param {String} [options.name] The name to use with Google Authenticator.\n * @param {Boolean} [options.qr_codes=false] (DEPRECATED. Do not use to prevent\n *   leaking of secret to a third party. Use your own QR code implementation.)\n *   Output QR code URLs for the token.\n * @param {Boolean} [options.google_auth_qr=false] (DEPRECATED. Do not use to\n *   prevent leaking of secret to a third party. Use your own QR code\n *   implementation.) Output a Google Authenticator otpauth:// QR code URL.\n * @return {Object}\n * @return {GeneratedSecret} The generated secret key.\n */\nexports.generateSecret = function generateSecret (options) {\n  // options\n  if (!options) options = {};\n  var length = options.length || 32;\n  var name = encodeURIComponent(options.name || 'SecretKey');\n  var qr_codes = options.qr_codes || false;\n  var google_auth_qr = options.google_auth_qr || false;\n  var otpauth_url = options.otpauth_url != null ? options.otpauth_url : true;\n  var symbols = true;\n\n  // turn off symbols only when explicity told to\n  if (options.symbols !== undefined && options.symbols === false) {\n    symbols = false;\n  }\n\n  // generate an ascii key\n  var key = this.generateSecretASCII(length, symbols);\n\n  // return a SecretKey with ascii, hex, and base32\n  var SecretKey = {};\n  SecretKey.ascii = key;\n  SecretKey.hex = Buffer(key, 'ascii').toString('hex');\n  SecretKey.base32 = base32.encode(Buffer(key)).toString().replace(/=/g, '');\n\n  // generate some qr codes if requested\n  if (qr_codes) {\n    console.warn('Speakeasy - Deprecation Notice - generateSecret() QR codes are deprecated and no longer supported. Please use your own QR code implementation.');\n    SecretKey.qr_code_ascii = 'https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl=' + encodeURIComponent(SecretKey.ascii);\n    SecretKey.qr_code_hex = 'https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl=' + encodeURIComponent(SecretKey.hex);\n    SecretKey.qr_code_base32 = 'https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl=' + encodeURIComponent(SecretKey.base32);\n  }\n\n  // add in the Google Authenticator-compatible otpauth URL\n  if (otpauth_url) {\n    SecretKey.otpauth_url = exports.otpauthURL({\n      secret: SecretKey.ascii,\n      label: name\n    });\n  }\n\n  // generate a QR code for use in Google Authenticator if requested\n  if (google_auth_qr) {\n    console.warn('Speakeasy - Deprecation Notice - generateSecret() Google Auth QR code is deprecated and no longer supported. Please use your own QR code implementation.');\n    SecretKey.google_auth_qr = 'https://chart.googleapis.com/chart?chs=166x166&chld=L|0&cht=qr&chl=' + encodeURIComponent(exports.otpauthURL({ secret: SecretKey.base32, label: name }));\n  }\n\n  return SecretKey;\n};\n\n// Backwards compatibility - generate_key is deprecated\nexports.generate_key = util.deprecate(function (options) {\n  return exports.generateSecret(options);\n}, 'Speakeasy - Deprecation Notice - `generate_key()` is depreciated, please use `generateSecret()` instead.');\n\n/**\n * Generates a key of a certain length (default 32) from A-Z, a-z, 0-9, and\n * symbols (if requested).\n *\n * @param  {Integer} [length=32]  The length of the key.\n * @param  {Boolean} [symbols=false] Whether to include symbols in the key.\n * @return {String} The generated key.\n */\nexports.generateSecretASCII = function generateSecretASCII (length, symbols) {\n  var bytes = crypto.randomBytes(length || 32);\n  var set = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz';\n  if (symbols) {\n    set += '!@#$%^&*()<>?/[]{},.:;';\n  }\n\n  var output = '';\n  for (var i = 0, l = bytes.length; i < l; i++) {\n    output += set[Math.floor(bytes[i] / 255.0 * (set.length - 1))];\n  }\n  return output;\n};\n\n// Backwards compatibility - generate_key_ascii is deprecated\nexports.generate_key_ascii = util.deprecate(function (length, symbols) {\n  return exports.generateSecretASCII(length, symbols);\n}, 'Speakeasy - Deprecation Notice - `generate_key_ascii()` is depreciated, please use `generateSecretASCII()` instead.');\n\n/**\n * Generate a Google Authenticator-compatible otpauth:// URL for passing the\n * secret to a mobile device to install the secret.\n *\n * Authenticator considers TOTP codes valid for 30 seconds. Additionally,\n * the app presents 6 digits codes to the user. According to the\n * documentation, the period and number of digits are currently ignored by\n * the app.\n *\n * To generate a suitable QR Code, pass the generated URL to a QR Code\n * generator, such as the `qr-image` module.\n *\n * @param {Object} options\n * @param {String} options.secret Shared secret key\n * @param {String} options.label Used to identify the account with which\n *   the secret key is associated, e.g. the user's email address.\n * @param {String} [options.type=\"totp\"] Either \"hotp\" or \"totp\".\n * @param {Integer} [options.counter] The initial counter value, required\n *   for HOTP.\n * @param {String} [options.issuer] The provider or service with which the\n *   secret key is associated.\n * @param {String} [options.algorithm=\"sha1\"] Hash algorithm (sha1, sha256,\n *   sha512).\n * @param {Integer} [options.digits=6] The number of digits for the one-time\n *   passcode. Currently ignored by Google Authenticator.\n * @param {Integer} [options.period=30] The length of time for which a TOTP\n *   code will be valid, in seconds. Currently ignored by Google\n *   Authenticator.\n * @param {String} [options.encoding] Key encoding (ascii, hex, base32,\n *   base64). If the key is not encoded in Base-32, it will be reencoded.\n * @return {String} A URL suitable for use with the Google Authenticator.\n * @throws Error if secret or label is missing, or if hotp is used and a\n    counter is missing, if the type is not one of `hotp` or `totp`, if the\n    number of digits is non-numeric, or an invalid period is used. Warns if\n    the number of digits is not either 6 or 8 (though 6 is the only one\n    supported by Google Authenticator), and if the hashihng algorithm is\n    not one of the supported SHA1, SHA256, or SHA512.\n * @see https://github.com/google/google-authenticator/wiki/Key-Uri-Format\n */\n\nexports.otpauthURL = function otpauthURL (options) {\n  // unpack options\n  var secret = options.secret;\n  var label = options.label;\n  var issuer = options.issuer;\n  var type = (options.type || 'totp').toLowerCase();\n  var counter = options.counter;\n  var algorithm = options.algorithm;\n  var digits = options.digits;\n  var period = options.period;\n  var encoding = options.encoding || 'ascii';\n\n  // validate type\n  switch (type) {\n    case 'totp':\n    case 'hotp':\n      break;\n    default:\n      throw new Error('Speakeasy - otpauthURL - Invalid type `' + type + '`; must be `hotp` or `totp`');\n  }\n\n  // validate required options\n  if (!secret) throw new Error('Speakeasy - otpauthURL - Missing secret');\n  if (!label) throw new Error('Speakeasy - otpauthURL - Missing label');\n\n  // require counter for HOTP\n  if (type === 'hotp' && (counter === null || typeof counter === 'undefined')) {\n    throw new Error('Speakeasy - otpauthURL - Missing counter value for HOTP');\n  }\n\n  // convert secret to base32\n  if (encoding !== 'base32') secret = new Buffer(secret, encoding);\n  if (Buffer.isBuffer(secret)) secret = base32.encode(secret);\n\n  // build query while validating\n  var query = {secret: secret};\n  if (issuer) query.issuer = issuer;\n\n  // validate algorithm\n  if (algorithm != null) {\n    switch (algorithm.toUpperCase()) {\n      case 'SHA1':\n      case 'SHA256':\n      case 'SHA512':\n        break;\n      default:\n        console.warn('Speakeasy - otpauthURL - Warning - Algorithm generally should be SHA1, SHA256, or SHA512');\n    }\n    query.algorithm = algorithm.toUpperCase();\n  }\n\n  // validate digits\n  if (digits != null) {\n    if (isNaN(digits)) {\n      throw new Error('Speakeasy - otpauthURL - Invalid digits `' + digits + '`');\n    } else {\n      switch (parseInt(digits, 10)) {\n        case 6:\n        case 8:\n          break;\n        default:\n          console.warn('Speakeasy - otpauthURL - Warning - Digits generally should be either 6 or 8');\n      }\n    }\n    query.digits = digits;\n  }\n\n  // validate period\n  if (period != null) {\n    period = parseInt(period, 10);\n    if (~~period !== period) {\n      throw new Error('Speakeasy - otpauthURL - Invalid period `' + period + '`');\n    }\n    query.period = period;\n  }\n\n  // return url\n  return url.format({\n    protocol: 'otpauth',\n    slashes: true,\n    hostname: type,\n    pathname: label,\n    query: query\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/speakeasy/index.js\n");

/***/ })

};
;