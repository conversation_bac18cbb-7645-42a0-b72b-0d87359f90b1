import { NextRequest, NextResponse } from 'next/server';
import { registerUser, generateToken } from '@/lib/auth';
import { systemLogDb, otpDb, adminSettingsDb } from '@/lib/database';
import { registerRateLimit } from '@/lib/rateLimiter';
import { validateInput, registerUserSchema } from '@/lib/validation';
import {
  withSecureErrorHandling,
  createValidationError,
  createRateLimitError,
  createSecureResponse
} from '@/lib/secureErrorHandler';

const registerHandler = async (request: NextRequest) => {
  // Check rate limiting first
  const rateLimitResult = registerRateLimit(request);
  if (!rateLimitResult.allowed) {
    const error = createRateLimitError(
      rateLimitResult.retryAfter,
      rateLimitResult.remaining,
      rateLimitResult.resetTime
    );
    return createSecureResponse(error);
  }

  // Check if registration is enabled
  const registrationEnabled = await adminSettingsDb.get('registrationEnabled');
  if (registrationEnabled === 'false') {
    return NextResponse.json(
      { success: false, error: 'Registration is currently disabled' },
      { status: 403 }
    );
  }

  const body = await request.json();
  const { otp, placementSide, agreeToTerms = true, ...userData } = body;

  // Comprehensive input validation
  const validationResult = validateInput(registerUserSchema, {
    ...userData,
    agreeToTerms,
  });

  if (!validationResult.success) {
    const error = createValidationError(validationResult.error);
    return createSecureResponse(error);
  }

  const validatedData = validationResult.data;

  // Verify OTP before registration
  if (!otp) {
    const error = createValidationError('OTP is required for registration');
    return createSecureResponse(error);
  }

  const otpRecord = await otpDb.findValid(validatedData.email, 'email_verification');
  if (!otpRecord || otpRecord.otp !== otp) {
    const error = createValidationError('Invalid or expired OTP. Please request a new one.');
    return createSecureResponse(error);
  }

  // Mark OTP as verified
  await otpDb.verify(otpRecord.id);

  // Extract side parameter from URL if present
  const url = new URL(request.url);
  const side = url.searchParams.get('side') as 'left' | 'right' | null;

  // Register user with validated data
  const user = await registerUser({
    email: validatedData.email,
    firstName: validatedData.firstName,
    lastName: validatedData.lastName,
    password: validatedData.password,
    referralCode: validatedData.referralCode || undefined,
    placementSide: placementSide || side || undefined,
  });

  // Log successful registration
  await systemLogDb.create({
    action: 'USER_REGISTRATION_SUCCESS',
    userId: user.id,
    details: {
      email: user.email,
      referralCode: validatedData.referralCode || null,
      placementSide: placementSide || side || null,
      registrationTime: new Date().toISOString(),
    },
    ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
  });

  // Generate token for automatic login after registration
  const token = generateToken({
    userId: user.id,
    email: user.email,
  });

  // Set secure HTTP-only cookie for automatic login
  const response = NextResponse.json({
    success: true,
    message: 'Registration successful',
    data: {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        referralId: user.referralId,
        kycStatus: user.kycStatus,
        role: user.role,
      },
      // Don't send token in response body for security
    },
  });

  // Enhanced cookie security
  response.cookies.set('auth-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    path: '/',
  });

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // Send welcome email (don't fail registration if email fails)
  try {
    const { emailNotificationService } = await import('@/lib/emailNotificationService');
    await emailNotificationService.sendWelcomeEmailNotification({
      userId: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
    });
  } catch (emailError) {
    console.error('Failed to send welcome email:', emailError);
    // Log email failure but don't fail the registration
    await systemLogDb.create({
      action: 'EMAIL_SEND_FAILED',
      userId: user.id,
      details: {
        type: 'welcome_email',
        error: emailError instanceof Error ? emailError.message : 'Unknown error',
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });
  }

  return response;
};

// Export the handler with secure error handling
export const POST = withSecureErrorHandling(registerHandler, {
  endpoint: '/api/auth/register',
  requireAuth: false,
});
