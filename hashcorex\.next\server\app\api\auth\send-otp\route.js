/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/send-otp/route";
exports.ids = ["app/api/auth/send-otp/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsend-otp%2Froute&page=%2Fapi%2Fauth%2Fsend-otp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsend-otp%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsend-otp%2Froute&page=%2Fapi%2Fauth%2Fsend-otp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsend-otp%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_auth_send_otp_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/send-otp/route.ts */ \"(rsc)/./src/app/api/auth/send-otp/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/send-otp/route\",\n        pathname: \"/api/auth/send-otp\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/send-otp/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Hash_Minings\\\\hashcorex\\\\src\\\\app\\\\api\\\\auth\\\\send-otp\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_Hash_Minings_hashcorex_src_app_api_auth_send_otp_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhdXRoJTJGc2VuZC1vdHAlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmF1dGglMkZzZW5kLW90cCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmF1dGglMkZzZW5kLW90cCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNkcmVhbSU1Q0Rlc2t0b3AlNUNIYXNoX01pbmluZ3MlNUNoYXNoY29yZXglNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2RyZWFtJTVDRGVza3RvcCU1Q0hhc2hfTWluaW5ncyU1Q2hhc2hjb3JleCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDMkM7QUFDeEg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhdXRoXFxcXHNlbmQtb3RwXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9hdXRoL3NlbmQtb3RwL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYXV0aC9zZW5kLW90cFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYXV0aC9zZW5kLW90cC9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGRyZWFtXFxcXERlc2t0b3BcXFxcSGFzaF9NaW5pbmdzXFxcXGhhc2hjb3JleFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhdXRoXFxcXHNlbmQtb3RwXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsend-otp%2Froute&page=%2Fapi%2Fauth%2Fsend-otp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsend-otp%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/send-otp/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/auth/send-otp/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_email__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/email */ \"(rsc)/./src/lib/email.ts\");\n/* harmony import */ var _lib_errorLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/errorLogger */ \"(rsc)/./src/lib/errorLogger.ts\");\n\n\n\n\n// POST - Send OTP for email verification or password reset\nasync function POST(request) {\n    try {\n        const { email, firstName, purpose = 'email_verification' } = await request.json();\n        if (!email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check email existence based on purpose\n        const existingUser = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.userDb.findByEmail(email);\n        if (purpose === 'email_verification') {\n            // For registration, email should not exist\n            if (existingUser) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Email is already registered'\n                }, {\n                    status: 400\n                });\n            }\n        } else if (purpose === 'password_reset') {\n            // For password reset, email must exist\n            if (!existingUser) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Email not found. Please check your email address.'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Generate OTP\n        const otp = (0,_lib_email__WEBPACK_IMPORTED_MODULE_2__.generateOTP)();\n        const expiresAt = new Date();\n        expiresAt.setMinutes(expiresAt.getMinutes() + 10); // 10 minutes expiry\n        // Save OTP to database\n        await _lib_database__WEBPACK_IMPORTED_MODULE_1__.otpDb.create({\n            email,\n            otp,\n            purpose,\n            expiresAt\n        });\n        // Determine email subject and template based on purpose\n        const emailSubject = purpose === 'password_reset' ? 'Password Reset - HashCoreX' : 'Email Verification - HashCoreX';\n        const emailTemplate = purpose === 'password_reset' ? 'password_reset_otp' : 'otp_verification';\n        // Create email log entry\n        const emailLog = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.create({\n            to: email,\n            subject: emailSubject,\n            template: emailTemplate,\n            status: 'PENDING'\n        });\n        // Send OTP email\n        try {\n            const userFirstName = existingUser?.firstName || firstName || '';\n            const emailSent = await _lib_email__WEBPACK_IMPORTED_MODULE_2__.emailService.sendOTPEmail(email, otp, userFirstName, purpose);\n            if (emailSent) {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'SENT');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: 'OTP sent successfully to your email',\n                    data: {\n                        email,\n                        expiresAt: expiresAt.toISOString()\n                    }\n                });\n            } else {\n                await _lib_database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', 'Email service error');\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'Failed to send OTP email. Please try again.'\n                }, {\n                    status: 500\n                });\n            }\n        } catch (emailError) {\n            await _lib_database__WEBPACK_IMPORTED_MODULE_1__.emailLogDb.updateStatus(emailLog.id, 'FAILED', emailError instanceof Error ? emailError.message : 'Unknown error');\n            console.error('Email sending error:', emailError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Failed to send OTP email. Please check your email configuration.'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Send OTP error:', error);\n        await _lib_errorLogger__WEBPACK_IMPORTED_MODULE_3__.ErrorLogger.logApiError(request, error, 'SEND_OTP_ERROR');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/send-otp/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminSettingsDb: () => (/* binding */ adminSettingsDb),\n/* harmony export */   binaryPointsDb: () => (/* binding */ binaryPointsDb),\n/* harmony export */   depositTransactionDb: () => (/* binding */ depositTransactionDb),\n/* harmony export */   emailLogDb: () => (/* binding */ emailLogDb),\n/* harmony export */   emailTemplateDb: () => (/* binding */ emailTemplateDb),\n/* harmony export */   miningUnitDb: () => (/* binding */ miningUnitDb),\n/* harmony export */   otpDb: () => (/* binding */ otpDb),\n/* harmony export */   referralDb: () => (/* binding */ referralDb),\n/* harmony export */   supportTicketDb: () => (/* binding */ supportTicketDb),\n/* harmony export */   systemLogDb: () => (/* binding */ systemLogDb),\n/* harmony export */   systemSettingsDb: () => (/* binding */ systemSettingsDb),\n/* harmony export */   ticketResponseDb: () => (/* binding */ ticketResponseDb),\n/* harmony export */   transactionDb: () => (/* binding */ transactionDb),\n/* harmony export */   userDb: () => (/* binding */ userDb),\n/* harmony export */   walletBalanceDb: () => (/* binding */ walletBalanceDb),\n/* harmony export */   withdrawalDb: () => (/* binding */ withdrawalDb)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n// User Database Operations\nconst userDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data: {\n                email: data.email,\n                firstName: data.firstName,\n                lastName: data.lastName,\n                password: data.password,\n                referralId: data.referralId || undefined\n            }\n        });\n    },\n    async findByEmail (email) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findById (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                miningUnits: true,\n                transactions: true,\n                binaryPoints: true\n            }\n        });\n    },\n    async findByReferralId (referralId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                referralId\n            }\n        });\n    },\n    async update (id, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    },\n    async updateKYCStatus (userId, status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                kycStatus: status\n            }\n        });\n    },\n    async updateWithdrawalAddress (email, withdrawalAddress) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                email\n            },\n            data: {\n                withdrawalAddress\n            }\n        });\n    },\n    async updateProfilePicture (id, profilePicture) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                profilePicture\n            },\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                referralId: true,\n                role: true,\n                kycStatus: true,\n                profilePicture: true,\n                createdAt: true,\n                updatedAt: true\n            }\n        });\n    },\n    async updatePassword (id, hashedPassword) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data: {\n                password: hashedPassword\n            }\n        });\n    }\n};\n// Mining Unit Database Operations\nconst miningUnitDb = {\n    async create (data) {\n        const expiryDate = new Date();\n        expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.create({\n            data: {\n                userId: data.userId,\n                thsAmount: data.thsAmount,\n                investmentAmount: data.investmentAmount,\n                dailyROI: data.dailyROI,\n                expiryDate\n            }\n        });\n    },\n    async findActiveByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                userId,\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            }\n        });\n    },\n    async updateTotalEarned (unitId, amount) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                totalEarned: {\n                    increment: amount\n                }\n            }\n        });\n    },\n    async expireUnit (unitId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: {\n                status: 'EXPIRED'\n            }\n        });\n    },\n    async findAllActive () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.findMany({\n            where: {\n                status: 'ACTIVE',\n                expiryDate: {\n                    gt: new Date()\n                }\n            },\n            include: {\n                user: true\n            }\n        });\n    },\n    async updateEarnings (unitId, earningType, amount) {\n        const updateData = {\n            totalEarned: {\n                increment: amount\n            }\n        };\n        switch(earningType){\n            case 'mining':\n                updateData.miningEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'referral':\n                updateData.referralEarnings = {\n                    increment: amount\n                };\n                break;\n            case 'binary':\n                updateData.binaryEarnings = {\n                    increment: amount\n                };\n                break;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.miningUnit.update({\n            where: {\n                id: unitId\n            },\n            data: updateData\n        });\n    }\n};\n// Transaction Database Operations\nconst transactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.create({\n            data: {\n                userId: data.userId,\n                type: data.type,\n                amount: data.amount,\n                description: data.description,\n                reference: data.reference,\n                status: data.status || 'PENDING'\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.types && filters.types.length > 0) {\n            where.type = {\n                in: filters.types\n            };\n        }\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        if (filters?.search) {\n            where.OR = [\n                {\n                    description: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    type: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    reference: {\n                        contains: filters.search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const include = filters?.includeUser ? {\n            user: {\n                select: {\n                    id: true,\n                    email: true,\n                    firstName: true,\n                    lastName: true\n                }\n            }\n        } : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findMany({\n            where,\n            include,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset\n        });\n    },\n    async updateStatus (transactionId, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.update({\n            where: {\n                id: transactionId\n            },\n            data: updateData\n        });\n    },\n    async findPendingByTypeAndDescription (userId, type, descriptionPattern) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.findFirst({\n            where: {\n                userId,\n                type,\n                description: {\n                    contains: descriptionPattern\n                },\n                status: 'PENDING'\n            }\n        });\n    },\n    async updateByReference (reference, type, status, additionalData) {\n        const updateData = {\n            status\n        };\n        if (additionalData?.amount !== undefined) {\n            updateData.amount = additionalData.amount;\n        }\n        if (additionalData?.description) {\n            updateData.description = additionalData.description;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.transaction.updateMany({\n            where: {\n                reference,\n                type,\n                status: 'PENDING'\n            },\n            data: updateData\n        });\n    }\n};\n// Referral Database Operations\nconst referralDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.create({\n            data: {\n                referrerId: data.referrerId,\n                referredId: data.referredId,\n                placementSide: data.placementSide\n            }\n        });\n    },\n    async findByReferrerId (referrerId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.referral.findMany({\n            where: {\n                referrerId\n            },\n            include: {\n                referred: {\n                    select: {\n                        id: true,\n                        email: true,\n                        createdAt: true\n                    }\n                }\n            }\n        });\n    }\n};\n// Binary Points Database Operations\nconst binaryPointsDb = {\n    async upsert (data) {\n        // Round to 2 decimal places to ensure precision\n        const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n        const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.upsert({\n            where: {\n                userId: data.userId\n            },\n            update: {\n                leftPoints: leftPoints !== undefined ? {\n                    increment: leftPoints\n                } : undefined,\n                rightPoints: rightPoints !== undefined ? {\n                    increment: rightPoints\n                } : undefined\n            },\n            create: {\n                userId: data.userId,\n                leftPoints: leftPoints || 0,\n                rightPoints: rightPoints || 0\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.findUnique({\n            where: {\n                userId\n            }\n        });\n    },\n    async resetPoints (userId, leftPoints, rightPoints) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.binaryPoints.update({\n            where: {\n                userId\n            },\n            data: {\n                leftPoints,\n                rightPoints,\n                flushDate: new Date()\n            }\n        });\n    }\n};\n// Withdrawal Database Operations\nconst withdrawalDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.create({\n            data: {\n                userId: data.userId,\n                amount: data.amount,\n                usdtAddress: data.usdtAddress\n            }\n        });\n    },\n    async findPending () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.findMany({\n            where: {\n                status: 'PENDING'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        kycStatus: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    },\n    async updateStatus (requestId, status, processedBy, txid, rejectionReason) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.withdrawalRequest.update({\n            where: {\n                id: requestId\n            },\n            data: {\n                status,\n                processedBy,\n                txid,\n                rejectionReason,\n                processedAt: new Date()\n            }\n        });\n    }\n};\n// Admin Settings Database Operations\nconst adminSettingsDb = {\n    async get (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value;\n    },\n    async set (key, value, updatedBy) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n            where: {\n                key\n            },\n            update: {\n                value\n            },\n            create: {\n                key,\n                value\n            }\n        });\n    },\n    async getAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany();\n    }\n};\n// System Logs\nconst systemLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.systemLog.create({\n            data: {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: data.details ? JSON.stringify(data.details) : null,\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            }\n        });\n    }\n};\n// Wallet Balance Database Operations\nconst walletBalanceDb = {\n    async getOrCreate (userId) {\n        let walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.findUnique({\n            where: {\n                userId\n            }\n        });\n        if (!walletBalance) {\n            walletBalance = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.create({\n                data: {\n                    userId,\n                    availableBalance: 0,\n                    pendingBalance: 0,\n                    totalDeposits: 0,\n                    totalWithdrawals: 0,\n                    totalEarnings: 0\n                }\n            });\n        }\n        return walletBalance;\n    },\n    async updateBalance (userId, updates) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                ...updates,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addDeposit (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalDeposits: wallet.totalDeposits + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async addEarnings (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance + amount,\n                totalEarnings: wallet.totalEarnings + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async deductWithdrawal (userId, amount) {\n        const wallet = await this.getOrCreate(userId);\n        if (wallet.availableBalance < amount) {\n            throw new Error('Insufficient balance');\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.walletBalance.update({\n            where: {\n                userId\n            },\n            data: {\n                availableBalance: wallet.availableBalance - amount,\n                totalWithdrawals: wallet.totalWithdrawals + amount,\n                lastUpdated: new Date()\n            }\n        });\n    },\n    async findByUserId (userId) {\n        return await this.getOrCreate(userId);\n    }\n};\n// Deposit Transaction Database Operations\nconst depositTransactionDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.create({\n            data: {\n                userId: data.userId,\n                transactionId: data.transactionId,\n                amount: data.amount,\n                usdtAmount: data.usdtAmount,\n                tronAddress: data.tronAddress,\n                senderAddress: data.senderAddress,\n                blockNumber: data.blockNumber,\n                blockTimestamp: data.blockTimestamp,\n                confirmations: data.confirmations || 0,\n                status: 'PENDING'\n            }\n        });\n    },\n    async findByTransactionId (transactionId) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findUnique({\n            where: {\n                transactionId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findByUserId (userId, filters) {\n        const where = {\n            userId\n        };\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 50,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async findAll (filters) {\n        const where = {};\n        if (filters?.status) {\n            where.status = filters.status;\n        }\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where,\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: filters?.limit || 100,\n            skip: filters?.offset,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateStatus (transactionId, status, updates) {\n        const updateData = {\n            status\n        };\n        if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n        if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n        if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n        if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: updateData\n        });\n    },\n    async markAsCompleted (transactionId) {\n        return await this.updateStatus(transactionId, 'COMPLETED', {\n            processedAt: new Date()\n        });\n    },\n    async markAsFailed (transactionId, reason) {\n        return await this.updateStatus(transactionId, 'FAILED', {\n            failureReason: reason,\n            processedAt: new Date()\n        });\n    },\n    async getPendingDeposits () {\n        return await this.findAll({\n            status: 'PENDING'\n        });\n    },\n    async getPendingVerificationDeposits () {\n        return await this.findAll({\n            status: 'PENDING_VERIFICATION'\n        });\n    },\n    async getWaitingForConfirmationsDeposits () {\n        return await this.findAll({\n            status: 'WAITING_FOR_CONFIRMATIONS'\n        });\n    },\n    async findByStatus (status) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.findMany({\n            where: {\n                status\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    async updateConfirmations (transactionId, confirmations) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.update({\n            where: {\n                transactionId\n            },\n            data: {\n                confirmations\n            }\n        });\n    },\n    async getDepositStats () {\n        const stats = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.aggregate({\n            _count: {\n                id: true\n            },\n            _sum: {\n                usdtAmount: true\n            },\n            where: {\n                status: {\n                    in: [\n                        'COMPLETED',\n                        'CONFIRMED'\n                    ]\n                }\n            }\n        });\n        const pendingCount = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.depositTransaction.count({\n            where: {\n                status: {\n                    in: [\n                        'PENDING',\n                        'PENDING_VERIFICATION',\n                        'WAITING_FOR_CONFIRMATIONS'\n                    ]\n                }\n            }\n        });\n        return {\n            totalDeposits: stats._count.id || 0,\n            totalAmount: stats._sum.usdtAmount || 0,\n            pendingDeposits: pendingCount\n        };\n    }\n};\n// Support Ticket Database Operations\nconst supportTicketDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findByUserId: async (userId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            where: {\n                userId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    findById: async (id)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findUnique({\n            where: {\n                id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    },\n    findAll: async ()=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.findMany({\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    updateStatus: async (id, status)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.supportTicket.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                updatedAt: new Date()\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                },\n                responses: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                email: true,\n                                firstName: true,\n                                lastName: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: 'asc'\n                    }\n                }\n            }\n        });\n    }\n};\n// Ticket Response Database Operations\nconst ticketResponseDb = {\n    create: async (data)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.create({\n            data,\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            }\n        });\n    },\n    findByTicketId: async (ticketId)=>{\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.ticketResponse.findMany({\n            where: {\n                ticketId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'asc'\n            }\n        });\n    }\n};\n// System Settings Database Operations\nconst systemSettingsDb = {\n    async getSetting (key) {\n        const setting = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findUnique({\n            where: {\n                key\n            }\n        });\n        return setting?.value || null;\n    },\n    async getSettings (keys) {\n        const settings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.findMany({\n            where: {\n                key: {\n                    in: keys\n                }\n            }\n        });\n        const result = {};\n        settings.forEach((setting)=>{\n            result[setting.key] = setting.value;\n        });\n        return result;\n    },\n    async updateSettings (settings) {\n        const updates = Object.entries(settings).map(([key, value])=>({\n                key,\n                value: typeof value === 'string' ? value : JSON.stringify(value)\n            }));\n        // Use transaction to update multiple settings\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(updates.map(({ key, value })=>_prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.adminSettings.upsert({\n                where: {\n                    key\n                },\n                update: {\n                    value\n                },\n                create: {\n                    key,\n                    value\n                }\n            })));\n    },\n    async getEmailSettings () {\n        const settings = await this.getSettings([\n            'smtpHost',\n            'smtpPort',\n            'smtpSecure',\n            'smtpUser',\n            'smtpPassword',\n            'fromName',\n            'fromEmail',\n            'emailEnabled'\n        ]);\n        return {\n            smtpHost: settings.smtpHost,\n            smtpPort: settings.smtpPort ? parseInt(settings.smtpPort) : 587,\n            smtpSecure: settings.smtpSecure === 'true',\n            smtpUser: settings.smtpUser,\n            smtpPassword: settings.smtpPassword,\n            fromName: settings.fromName || 'HashCoreX',\n            fromEmail: settings.fromEmail,\n            emailEnabled: settings.emailEnabled !== 'false'\n        };\n    },\n    async updateEmailSettings (emailSettings) {\n        const settings = {};\n        if (emailSettings.smtpHost !== undefined) settings.smtpHost = emailSettings.smtpHost;\n        if (emailSettings.smtpPort !== undefined) settings.smtpPort = emailSettings.smtpPort.toString();\n        if (emailSettings.smtpSecure !== undefined) settings.smtpSecure = emailSettings.smtpSecure.toString();\n        if (emailSettings.smtpUser !== undefined) settings.smtpUser = emailSettings.smtpUser;\n        if (emailSettings.smtpPassword !== undefined) settings.smtpPassword = emailSettings.smtpPassword;\n        if (emailSettings.fromName !== undefined) settings.fromName = emailSettings.fromName;\n        if (emailSettings.fromEmail !== undefined) settings.fromEmail = emailSettings.fromEmail;\n        if (emailSettings.emailEnabled !== undefined) settings.emailEnabled = emailSettings.emailEnabled.toString();\n        await this.updateSettings(settings);\n    },\n    async getEmailTemplate (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name,\n                isActive: true\n            }\n        });\n    }\n};\n// OTP Verification Database Operations\nconst otpDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.create({\n            data\n        });\n    },\n    async findValid (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: false,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async verify (id) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.update({\n            where: {\n                id\n            },\n            data: {\n                verified: true\n            }\n        });\n    },\n    async findVerified (email, purpose) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.findFirst({\n            where: {\n                email,\n                purpose,\n                verified: true,\n                expiresAt: {\n                    gt: new Date()\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    },\n    async cleanup () {\n        // Remove expired OTPs\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.oTPVerification.deleteMany({\n            where: {\n                expiresAt: {\n                    lt: new Date()\n                }\n            }\n        });\n    }\n};\n// Email Template Database Operations\nconst emailTemplateDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.create({\n            data\n        });\n    },\n    async findAll () {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findMany({\n            orderBy: {\n                name: 'asc'\n            }\n        });\n    },\n    async findByName (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.findUnique({\n            where: {\n                name\n            }\n        });\n    },\n    async update (name, data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.update({\n            where: {\n                name\n            },\n            data\n        });\n    },\n    async delete (name) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailTemplate.delete({\n            where: {\n                name\n            }\n        });\n    }\n};\n// Email Log Database Operations\nconst emailLogDb = {\n    async create (data) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.create({\n            data\n        });\n    },\n    async updateStatus (id, status, error) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.update({\n            where: {\n                id\n            },\n            data: {\n                status,\n                error,\n                sentAt: status === 'SENT' ? new Date() : undefined\n            }\n        });\n    },\n    async findRecent (limit = 50) {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.emailLog.findMany({\n            take: limit,\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email.ts":
/*!**************************!*\
  !*** ./src/lib/email.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emailService: () => (/* binding */ emailService),\n/* harmony export */   generateOTP: () => (/* binding */ generateOTP),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\nclass EmailService {\n    async getEmailConfig() {\n        try {\n            const settings = await _database__WEBPACK_IMPORTED_MODULE_1__.systemSettingsDb.getEmailSettings();\n            if (!settings || !settings.smtpHost || !settings.smtpUser || !settings.smtpPassword) {\n                console.warn('Email configuration not found or incomplete');\n                return null;\n            }\n            return {\n                host: settings.smtpHost,\n                port: settings.smtpPort || 587,\n                secure: settings.smtpSecure || false,\n                user: settings.smtpUser,\n                password: settings.smtpPassword,\n                fromName: settings.fromName || 'HashCoreX',\n                fromEmail: settings.fromEmail || settings.smtpUser\n            };\n        } catch (error) {\n            console.error('Failed to get email configuration:', error);\n            return null;\n        }\n    }\n    async initializeTransporter() {\n        try {\n            this.config = await this.getEmailConfig();\n            if (!this.config) {\n                console.warn('Email configuration not available - email service disabled');\n                return false;\n            }\n            // Validate required fields\n            if (!this.config.host || !this.config.user || !this.config.password) {\n                console.error('Email configuration incomplete - missing host, user, or password');\n                return false;\n            }\n            this.transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n                host: this.config.host,\n                port: this.config.port,\n                secure: this.config.secure,\n                auth: {\n                    user: this.config.user,\n                    pass: this.config.password\n                },\n                tls: {\n                    rejectUnauthorized: false\n                },\n                connectionTimeout: 10000,\n                greetingTimeout: 5000,\n                socketTimeout: 10000\n            });\n            // Verify connection with timeout\n            const verifyPromise = this.transporter.verify();\n            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error('Email verification timeout')), 15000));\n            await Promise.race([\n                verifyPromise,\n                timeoutPromise\n            ]);\n            console.log('Email transporter initialized and verified successfully');\n            return true;\n        } catch (error) {\n            console.error('Failed to initialize email transporter:', error);\n            this.transporter = null;\n            this.config = null;\n            return false;\n        }\n    }\n    async sendEmail(emailData) {\n        try {\n            // Check if email service is configured\n            if (!this.transporter || !this.config) {\n                console.log('Email transporter not initialized, attempting to initialize...');\n                const initialized = await this.initializeTransporter();\n                if (!initialized) {\n                    console.warn('Email service not configured - skipping email send');\n                    return false; // Return false instead of throwing error\n                }\n            }\n            // Validate email data\n            if (!emailData.to || !emailData.subject) {\n                console.error('Invalid email data - missing recipient or subject');\n                return false;\n            }\n            const mailOptions = {\n                from: `\"${this.config.fromName}\" <${this.config.fromEmail}>`,\n                to: emailData.to,\n                subject: emailData.subject,\n                html: emailData.html,\n                text: emailData.text\n            };\n            // Send email with timeout\n            const sendPromise = this.transporter.sendMail(mailOptions);\n            const timeoutPromise = new Promise((_, reject)=>setTimeout(()=>reject(new Error('Email send timeout')), 30000));\n            const result = await Promise.race([\n                sendPromise,\n                timeoutPromise\n            ]);\n            console.log('Email sent successfully:', result.messageId);\n            return true;\n        } catch (error) {\n            console.error('Failed to send email:', error);\n            // Reset transporter on error to force reinitialization\n            this.transporter = null;\n            this.config = null;\n            return false;\n        }\n    }\n    async sendOTPEmail(email, otp, firstName, purpose = 'email_verification') {\n        const templateName = purpose === 'password_reset' ? 'password_reset_otp' : 'otp_verification';\n        const template = await this.getEmailTemplate(templateName);\n        if (!template) {\n            console.error(`Email template '${templateName}' not found. Please ensure email templates are seeded.`);\n            return false;\n        }\n        // Use custom template\n        let html = template.htmlContent;\n        let text = template.textContent || '';\n        // Replace placeholders\n        html = html.replace(/{{firstName}}/g, firstName || 'User');\n        html = html.replace(/{{otp}}/g, otp);\n        text = text.replace(/{{firstName}}/g, firstName || 'User');\n        text = text.replace(/{{otp}}/g, otp);\n        return await this.sendEmail({\n            to: email,\n            subject: template.subject,\n            html,\n            text\n        });\n    }\n    async getEmailTemplate(templateName) {\n        try {\n            const template = await _database__WEBPACK_IMPORTED_MODULE_1__.systemSettingsDb.getEmailTemplate(templateName);\n            return template;\n        } catch (error) {\n            console.error('Failed to get email template:', error);\n            return null;\n        }\n    }\n    async testConnection() {\n        try {\n            // Get fresh config\n            this.config = await this.getEmailConfig();\n            if (!this.config) {\n                console.error('Email configuration not found or incomplete');\n                return false;\n            }\n            // Create transporter\n            this.transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransport({\n                host: this.config.host,\n                port: this.config.port,\n                secure: this.config.secure,\n                auth: {\n                    user: this.config.user,\n                    pass: this.config.password\n                },\n                tls: {\n                    rejectUnauthorized: false\n                }\n            });\n            // Test connection\n            await this.transporter.verify();\n            console.log('Email connection test successful');\n            return true;\n        } catch (error) {\n            console.error('Email connection test failed:', error);\n            this.transporter = null;\n            throw error; // Re-throw to get specific error message\n        }\n    }\n    constructor(){\n        this.transporter = null;\n        this.config = null;\n    }\n}\n// Export singleton instance\nconst emailService = new EmailService();\n// Utility functions\nconst generateOTP = ()=>{\n    return Math.floor(100000 + Math.random() * 900000).toString();\n};\nconst isValidEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2VtYWlsLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW9DO0FBQ1U7QUF5QjlDLE1BQU1FO0lBSUosTUFBTUMsaUJBQThDO1FBQ2xELElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1ILHVEQUFnQkEsQ0FBQ0ksZ0JBQWdCO1lBQ3hELElBQUksQ0FBQ0QsWUFBWSxDQUFDQSxTQUFTRSxRQUFRLElBQUksQ0FBQ0YsU0FBU0csUUFBUSxJQUFJLENBQUNILFNBQVNJLFlBQVksRUFBRTtnQkFDbkZDLFFBQVFDLElBQUksQ0FBQztnQkFDYixPQUFPO1lBQ1Q7WUFFQSxPQUFPO2dCQUNMQyxNQUFNUCxTQUFTRSxRQUFRO2dCQUN2Qk0sTUFBTVIsU0FBU1MsUUFBUSxJQUFJO2dCQUMzQkMsUUFBUVYsU0FBU1csVUFBVSxJQUFJO2dCQUMvQkMsTUFBTVosU0FBU0csUUFBUTtnQkFDdkJVLFVBQVViLFNBQVNJLFlBQVk7Z0JBQy9CVSxVQUFVZCxTQUFTYyxRQUFRLElBQUk7Z0JBQy9CQyxXQUFXZixTQUFTZSxTQUFTLElBQUlmLFNBQVNHLFFBQVE7WUFDcEQ7UUFDRixFQUFFLE9BQU9hLE9BQU87WUFDZFgsUUFBUVcsS0FBSyxDQUFDLHNDQUFzQ0E7WUFDcEQsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNQyx3QkFBMEM7UUFDOUMsSUFBSTtZQUNGLElBQUksQ0FBQ0MsTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDbkIsY0FBYztZQUN2QyxJQUFJLENBQUMsSUFBSSxDQUFDbUIsTUFBTSxFQUFFO2dCQUNoQmIsUUFBUUMsSUFBSSxDQUFDO2dCQUNiLE9BQU87WUFDVDtZQUVBLDJCQUEyQjtZQUMzQixJQUFJLENBQUMsSUFBSSxDQUFDWSxNQUFNLENBQUNYLElBQUksSUFBSSxDQUFDLElBQUksQ0FBQ1csTUFBTSxDQUFDTixJQUFJLElBQUksQ0FBQyxJQUFJLENBQUNNLE1BQU0sQ0FBQ0wsUUFBUSxFQUFFO2dCQUNuRVIsUUFBUVcsS0FBSyxDQUFDO2dCQUNkLE9BQU87WUFDVDtZQUVBLElBQUksQ0FBQ0csV0FBVyxHQUFHdkIsdURBQTBCLENBQUM7Z0JBQzVDVyxNQUFNLElBQUksQ0FBQ1csTUFBTSxDQUFDWCxJQUFJO2dCQUN0QkMsTUFBTSxJQUFJLENBQUNVLE1BQU0sQ0FBQ1YsSUFBSTtnQkFDdEJFLFFBQVEsSUFBSSxDQUFDUSxNQUFNLENBQUNSLE1BQU07Z0JBQzFCVyxNQUFNO29CQUNKVCxNQUFNLElBQUksQ0FBQ00sTUFBTSxDQUFDTixJQUFJO29CQUN0QlUsTUFBTSxJQUFJLENBQUNKLE1BQU0sQ0FBQ0wsUUFBUTtnQkFDNUI7Z0JBQ0FVLEtBQUs7b0JBQ0hDLG9CQUFvQjtnQkFDdEI7Z0JBQ0FDLG1CQUFtQjtnQkFDbkJDLGlCQUFpQjtnQkFDakJDLGVBQWU7WUFDakI7WUFFQSxpQ0FBaUM7WUFDakMsTUFBTUMsZ0JBQWdCLElBQUksQ0FBQ1QsV0FBVyxDQUFDVSxNQUFNO1lBQzdDLE1BQU1DLGlCQUFpQixJQUFJQyxRQUFRLENBQUNDLEdBQUdDLFNBQ3JDQyxXQUFXLElBQU1ELE9BQU8sSUFBSUUsTUFBTSxnQ0FBZ0M7WUFHcEUsTUFBTUosUUFBUUssSUFBSSxDQUFDO2dCQUFDUjtnQkFBZUU7YUFBZTtZQUNsRHpCLFFBQVFnQyxHQUFHLENBQUM7WUFDWixPQUFPO1FBQ1QsRUFBRSxPQUFPckIsT0FBTztZQUNkWCxRQUFRVyxLQUFLLENBQUMsMkNBQTJDQTtZQUN6RCxJQUFJLENBQUNHLFdBQVcsR0FBRztZQUNuQixJQUFJLENBQUNELE1BQU0sR0FBRztZQUNkLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTW9CLFVBQVVDLFNBQW9CLEVBQW9CO1FBQ3RELElBQUk7WUFDRix1Q0FBdUM7WUFDdkMsSUFBSSxDQUFDLElBQUksQ0FBQ3BCLFdBQVcsSUFBSSxDQUFDLElBQUksQ0FBQ0QsTUFBTSxFQUFFO2dCQUNyQ2IsUUFBUWdDLEdBQUcsQ0FBQztnQkFDWixNQUFNRyxjQUFjLE1BQU0sSUFBSSxDQUFDdkIscUJBQXFCO2dCQUNwRCxJQUFJLENBQUN1QixhQUFhO29CQUNoQm5DLFFBQVFDLElBQUksQ0FBQztvQkFDYixPQUFPLE9BQU8seUNBQXlDO2dCQUN6RDtZQUNGO1lBRUEsc0JBQXNCO1lBQ3RCLElBQUksQ0FBQ2lDLFVBQVVFLEVBQUUsSUFBSSxDQUFDRixVQUFVRyxPQUFPLEVBQUU7Z0JBQ3ZDckMsUUFBUVcsS0FBSyxDQUFDO2dCQUNkLE9BQU87WUFDVDtZQUVBLE1BQU0yQixjQUFjO2dCQUNsQkMsTUFBTSxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMxQixNQUFNLENBQUVKLFFBQVEsQ0FBQyxHQUFHLEVBQUUsSUFBSSxDQUFDSSxNQUFNLENBQUVILFNBQVMsQ0FBQyxDQUFDLENBQUM7Z0JBQzlEMEIsSUFBSUYsVUFBVUUsRUFBRTtnQkFDaEJDLFNBQVNILFVBQVVHLE9BQU87Z0JBQzFCRyxNQUFNTixVQUFVTSxJQUFJO2dCQUNwQkMsTUFBTVAsVUFBVU8sSUFBSTtZQUN0QjtZQUVBLDBCQUEwQjtZQUMxQixNQUFNQyxjQUFjLElBQUksQ0FBQzVCLFdBQVcsQ0FBRTZCLFFBQVEsQ0FBQ0w7WUFDL0MsTUFBTWIsaUJBQWlCLElBQUlDLFFBQVEsQ0FBQ0MsR0FBR0MsU0FDckNDLFdBQVcsSUFBTUQsT0FBTyxJQUFJRSxNQUFNLHdCQUF3QjtZQUc1RCxNQUFNYyxTQUFTLE1BQU1sQixRQUFRSyxJQUFJLENBQUM7Z0JBQUNXO2dCQUFhakI7YUFBZTtZQUMvRHpCLFFBQVFnQyxHQUFHLENBQUMsNEJBQTRCWSxPQUFPQyxTQUFTO1lBQ3hELE9BQU87UUFDVCxFQUFFLE9BQU9sQyxPQUFPO1lBQ2RYLFFBQVFXLEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDLHVEQUF1RDtZQUN2RCxJQUFJLENBQUNHLFdBQVcsR0FBRztZQUNuQixJQUFJLENBQUNELE1BQU0sR0FBRztZQUNkLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTWlDLGFBQWFDLEtBQWEsRUFBRUMsR0FBVyxFQUFFQyxTQUFrQixFQUFFQyxVQUFrQixvQkFBb0IsRUFBb0I7UUFDM0gsTUFBTUMsZUFBZUQsWUFBWSxtQkFBbUIsdUJBQXVCO1FBQzNFLE1BQU1FLFdBQVcsTUFBTSxJQUFJLENBQUNDLGdCQUFnQixDQUFDRjtRQUU3QyxJQUFJLENBQUNDLFVBQVU7WUFDYnBELFFBQVFXLEtBQUssQ0FBQyxDQUFDLGdCQUFnQixFQUFFd0MsYUFBYSxzREFBc0QsQ0FBQztZQUNyRyxPQUFPO1FBQ1Q7UUFFQSxzQkFBc0I7UUFDdEIsSUFBSVgsT0FBT1ksU0FBU0UsV0FBVztRQUMvQixJQUFJYixPQUFPVyxTQUFTRyxXQUFXLElBQUk7UUFFbkMsdUJBQXVCO1FBQ3ZCZixPQUFPQSxLQUFLZ0IsT0FBTyxDQUFDLGtCQUFrQlAsYUFBYTtRQUNuRFQsT0FBT0EsS0FBS2dCLE9BQU8sQ0FBQyxZQUFZUjtRQUNoQ1AsT0FBT0EsS0FBS2UsT0FBTyxDQUFDLGtCQUFrQlAsYUFBYTtRQUNuRFIsT0FBT0EsS0FBS2UsT0FBTyxDQUFDLFlBQVlSO1FBRWhDLE9BQU8sTUFBTSxJQUFJLENBQUNmLFNBQVMsQ0FBQztZQUMxQkcsSUFBSVc7WUFDSlYsU0FBU2UsU0FBU2YsT0FBTztZQUN6Qkc7WUFDQUM7UUFDRjtJQUNGO0lBRUEsTUFBTVksaUJBQWlCRixZQUFvQixFQUFpQztRQUMxRSxJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNNUQsdURBQWdCQSxDQUFDNkQsZ0JBQWdCLENBQUNGO1lBQ3pELE9BQU9DO1FBQ1QsRUFBRSxPQUFPekMsT0FBTztZQUNkWCxRQUFRVyxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU04QyxpQkFBbUM7UUFDdkMsSUFBSTtZQUNGLG1CQUFtQjtZQUNuQixJQUFJLENBQUM1QyxNQUFNLEdBQUcsTUFBTSxJQUFJLENBQUNuQixjQUFjO1lBQ3ZDLElBQUksQ0FBQyxJQUFJLENBQUNtQixNQUFNLEVBQUU7Z0JBQ2hCYixRQUFRVyxLQUFLLENBQUM7Z0JBQ2QsT0FBTztZQUNUO1lBRUEscUJBQXFCO1lBQ3JCLElBQUksQ0FBQ0csV0FBVyxHQUFHdkIsdURBQTBCLENBQUM7Z0JBQzVDVyxNQUFNLElBQUksQ0FBQ1csTUFBTSxDQUFDWCxJQUFJO2dCQUN0QkMsTUFBTSxJQUFJLENBQUNVLE1BQU0sQ0FBQ1YsSUFBSTtnQkFDdEJFLFFBQVEsSUFBSSxDQUFDUSxNQUFNLENBQUNSLE1BQU07Z0JBQzFCVyxNQUFNO29CQUNKVCxNQUFNLElBQUksQ0FBQ00sTUFBTSxDQUFDTixJQUFJO29CQUN0QlUsTUFBTSxJQUFJLENBQUNKLE1BQU0sQ0FBQ0wsUUFBUTtnQkFDNUI7Z0JBQ0FVLEtBQUs7b0JBQ0hDLG9CQUFvQjtnQkFDdEI7WUFDRjtZQUVBLGtCQUFrQjtZQUNsQixNQUFNLElBQUksQ0FBQ0wsV0FBVyxDQUFDVSxNQUFNO1lBQzdCeEIsUUFBUWdDLEdBQUcsQ0FBQztZQUNaLE9BQU87UUFDVCxFQUFFLE9BQU9yQixPQUFPO1lBQ2RYLFFBQVFXLEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DLElBQUksQ0FBQ0csV0FBVyxHQUFHO1lBQ25CLE1BQU1ILE9BQU8seUNBQXlDO1FBQ3hEO0lBQ0Y7O2FBMUxRRyxjQUE2QzthQUM3Q0QsU0FBNkI7O0FBMEx2QztBQUVBLDRCQUE0QjtBQUNyQixNQUFNNkMsZUFBZSxJQUFJakUsZUFBZTtBQUUvQyxvQkFBb0I7QUFDYixNQUFNa0UsY0FBYztJQUN6QixPQUFPQyxLQUFLQyxLQUFLLENBQUMsU0FBU0QsS0FBS0UsTUFBTSxLQUFLLFFBQVFDLFFBQVE7QUFDN0QsRUFBRTtBQUVLLE1BQU1DLGVBQWUsQ0FBQ2pCO0lBQzNCLE1BQU1rQixhQUFhO0lBQ25CLE9BQU9BLFdBQVdDLElBQUksQ0FBQ25CO0FBQ3pCLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxcZW1haWwudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG5vZGVtYWlsZXIgZnJvbSAnbm9kZW1haWxlcic7XG5pbXBvcnQgeyBzeXN0ZW1TZXR0aW5nc0RiIH0gZnJvbSAnLi9kYXRhYmFzZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgRW1haWxDb25maWcge1xuICBob3N0OiBzdHJpbmc7XG4gIHBvcnQ6IG51bWJlcjtcbiAgc2VjdXJlOiBib29sZWFuO1xuICB1c2VyOiBzdHJpbmc7XG4gIHBhc3N3b3JkOiBzdHJpbmc7XG4gIGZyb21OYW1lOiBzdHJpbmc7XG4gIGZyb21FbWFpbDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEVtYWlsVGVtcGxhdGUge1xuICBzdWJqZWN0OiBzdHJpbmc7XG4gIGh0bWxDb250ZW50OiBzdHJpbmc7XG4gIHRleHRDb250ZW50Pzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEVtYWlsRGF0YSB7XG4gIHRvOiBzdHJpbmc7XG4gIHN1YmplY3Q6IHN0cmluZztcbiAgaHRtbDogc3RyaW5nO1xuICB0ZXh0Pzogc3RyaW5nO1xufVxuXG5jbGFzcyBFbWFpbFNlcnZpY2Uge1xuICBwcml2YXRlIHRyYW5zcG9ydGVyOiBub2RlbWFpbGVyLlRyYW5zcG9ydGVyIHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgY29uZmlnOiBFbWFpbENvbmZpZyB8IG51bGwgPSBudWxsO1xuXG4gIGFzeW5jIGdldEVtYWlsQ29uZmlnKCk6IFByb21pc2U8RW1haWxDb25maWcgfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNldHRpbmdzID0gYXdhaXQgc3lzdGVtU2V0dGluZ3NEYi5nZXRFbWFpbFNldHRpbmdzKCk7XG4gICAgICBpZiAoIXNldHRpbmdzIHx8ICFzZXR0aW5ncy5zbXRwSG9zdCB8fCAhc2V0dGluZ3Muc210cFVzZXIgfHwgIXNldHRpbmdzLnNtdHBQYXNzd29yZCkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0VtYWlsIGNvbmZpZ3VyYXRpb24gbm90IGZvdW5kIG9yIGluY29tcGxldGUnKTtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGhvc3Q6IHNldHRpbmdzLnNtdHBIb3N0LFxuICAgICAgICBwb3J0OiBzZXR0aW5ncy5zbXRwUG9ydCB8fCA1ODcsXG4gICAgICAgIHNlY3VyZTogc2V0dGluZ3Muc210cFNlY3VyZSB8fCBmYWxzZSxcbiAgICAgICAgdXNlcjogc2V0dGluZ3Muc210cFVzZXIsXG4gICAgICAgIHBhc3N3b3JkOiBzZXR0aW5ncy5zbXRwUGFzc3dvcmQsXG4gICAgICAgIGZyb21OYW1lOiBzZXR0aW5ncy5mcm9tTmFtZSB8fCAnSGFzaENvcmVYJyxcbiAgICAgICAgZnJvbUVtYWlsOiBzZXR0aW5ncy5mcm9tRW1haWwgfHwgc2V0dGluZ3Muc210cFVzZXIsXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2V0IGVtYWlsIGNvbmZpZ3VyYXRpb246JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgaW5pdGlhbGl6ZVRyYW5zcG9ydGVyKCk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIHRyeSB7XG4gICAgICB0aGlzLmNvbmZpZyA9IGF3YWl0IHRoaXMuZ2V0RW1haWxDb25maWcoKTtcbiAgICAgIGlmICghdGhpcy5jb25maWcpIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdFbWFpbCBjb25maWd1cmF0aW9uIG5vdCBhdmFpbGFibGUgLSBlbWFpbCBzZXJ2aWNlIGRpc2FibGVkJyk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgLy8gVmFsaWRhdGUgcmVxdWlyZWQgZmllbGRzXG4gICAgICBpZiAoIXRoaXMuY29uZmlnLmhvc3QgfHwgIXRoaXMuY29uZmlnLnVzZXIgfHwgIXRoaXMuY29uZmlnLnBhc3N3b3JkKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0VtYWlsIGNvbmZpZ3VyYXRpb24gaW5jb21wbGV0ZSAtIG1pc3NpbmcgaG9zdCwgdXNlciwgb3IgcGFzc3dvcmQnKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuXG4gICAgICB0aGlzLnRyYW5zcG9ydGVyID0gbm9kZW1haWxlci5jcmVhdGVUcmFuc3BvcnQoe1xuICAgICAgICBob3N0OiB0aGlzLmNvbmZpZy5ob3N0LFxuICAgICAgICBwb3J0OiB0aGlzLmNvbmZpZy5wb3J0LFxuICAgICAgICBzZWN1cmU6IHRoaXMuY29uZmlnLnNlY3VyZSxcbiAgICAgICAgYXV0aDoge1xuICAgICAgICAgIHVzZXI6IHRoaXMuY29uZmlnLnVzZXIsXG4gICAgICAgICAgcGFzczogdGhpcy5jb25maWcucGFzc3dvcmQsXG4gICAgICAgIH0sXG4gICAgICAgIHRsczoge1xuICAgICAgICAgIHJlamVjdFVuYXV0aG9yaXplZDogZmFsc2UsIC8vIEZvciBkZXZlbG9wbWVudC90ZXN0aW5nXG4gICAgICAgIH0sXG4gICAgICAgIGNvbm5lY3Rpb25UaW1lb3V0OiAxMDAwMCwgLy8gMTAgc2Vjb25kc1xuICAgICAgICBncmVldGluZ1RpbWVvdXQ6IDUwMDAsIC8vIDUgc2Vjb25kc1xuICAgICAgICBzb2NrZXRUaW1lb3V0OiAxMDAwMCwgLy8gMTAgc2Vjb25kc1xuICAgICAgfSk7XG5cbiAgICAgIC8vIFZlcmlmeSBjb25uZWN0aW9uIHdpdGggdGltZW91dFxuICAgICAgY29uc3QgdmVyaWZ5UHJvbWlzZSA9IHRoaXMudHJhbnNwb3J0ZXIudmVyaWZ5KCk7XG4gICAgICBjb25zdCB0aW1lb3V0UHJvbWlzZSA9IG5ldyBQcm9taXNlKChfLCByZWplY3QpID0+XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignRW1haWwgdmVyaWZpY2F0aW9uIHRpbWVvdXQnKSksIDE1MDAwKVxuICAgICAgKTtcblxuICAgICAgYXdhaXQgUHJvbWlzZS5yYWNlKFt2ZXJpZnlQcm9taXNlLCB0aW1lb3V0UHJvbWlzZV0pO1xuICAgICAgY29uc29sZS5sb2coJ0VtYWlsIHRyYW5zcG9ydGVyIGluaXRpYWxpemVkIGFuZCB2ZXJpZmllZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSBlbWFpbCB0cmFuc3BvcnRlcjonLCBlcnJvcik7XG4gICAgICB0aGlzLnRyYW5zcG9ydGVyID0gbnVsbDtcbiAgICAgIHRoaXMuY29uZmlnID0gbnVsbDtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBzZW5kRW1haWwoZW1haWxEYXRhOiBFbWFpbERhdGEpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgaWYgZW1haWwgc2VydmljZSBpcyBjb25maWd1cmVkXG4gICAgICBpZiAoIXRoaXMudHJhbnNwb3J0ZXIgfHwgIXRoaXMuY29uZmlnKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdFbWFpbCB0cmFuc3BvcnRlciBub3QgaW5pdGlhbGl6ZWQsIGF0dGVtcHRpbmcgdG8gaW5pdGlhbGl6ZS4uLicpO1xuICAgICAgICBjb25zdCBpbml0aWFsaXplZCA9IGF3YWl0IHRoaXMuaW5pdGlhbGl6ZVRyYW5zcG9ydGVyKCk7XG4gICAgICAgIGlmICghaW5pdGlhbGl6ZWQpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ0VtYWlsIHNlcnZpY2Ugbm90IGNvbmZpZ3VyZWQgLSBza2lwcGluZyBlbWFpbCBzZW5kJyk7XG4gICAgICAgICAgcmV0dXJuIGZhbHNlOyAvLyBSZXR1cm4gZmFsc2UgaW5zdGVhZCBvZiB0aHJvd2luZyBlcnJvclxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIFZhbGlkYXRlIGVtYWlsIGRhdGFcbiAgICAgIGlmICghZW1haWxEYXRhLnRvIHx8ICFlbWFpbERhdGEuc3ViamVjdCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdJbnZhbGlkIGVtYWlsIGRhdGEgLSBtaXNzaW5nIHJlY2lwaWVudCBvciBzdWJqZWN0Jyk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgbWFpbE9wdGlvbnMgPSB7XG4gICAgICAgIGZyb206IGBcIiR7dGhpcy5jb25maWchLmZyb21OYW1lfVwiIDwke3RoaXMuY29uZmlnIS5mcm9tRW1haWx9PmAsXG4gICAgICAgIHRvOiBlbWFpbERhdGEudG8sXG4gICAgICAgIHN1YmplY3Q6IGVtYWlsRGF0YS5zdWJqZWN0LFxuICAgICAgICBodG1sOiBlbWFpbERhdGEuaHRtbCxcbiAgICAgICAgdGV4dDogZW1haWxEYXRhLnRleHQsXG4gICAgICB9O1xuXG4gICAgICAvLyBTZW5kIGVtYWlsIHdpdGggdGltZW91dFxuICAgICAgY29uc3Qgc2VuZFByb21pc2UgPSB0aGlzLnRyYW5zcG9ydGVyIS5zZW5kTWFpbChtYWlsT3B0aW9ucyk7XG4gICAgICBjb25zdCB0aW1lb3V0UHJvbWlzZSA9IG5ldyBQcm9taXNlKChfLCByZWplY3QpID0+XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignRW1haWwgc2VuZCB0aW1lb3V0JykpLCAzMDAwMClcbiAgICAgICk7XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFByb21pc2UucmFjZShbc2VuZFByb21pc2UsIHRpbWVvdXRQcm9taXNlXSk7XG4gICAgICBjb25zb2xlLmxvZygnRW1haWwgc2VudCBzdWNjZXNzZnVsbHk6JywgcmVzdWx0Lm1lc3NhZ2VJZCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNlbmQgZW1haWw6JywgZXJyb3IpO1xuICAgICAgLy8gUmVzZXQgdHJhbnNwb3J0ZXIgb24gZXJyb3IgdG8gZm9yY2UgcmVpbml0aWFsaXphdGlvblxuICAgICAgdGhpcy50cmFuc3BvcnRlciA9IG51bGw7XG4gICAgICB0aGlzLmNvbmZpZyA9IG51bGw7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgc2VuZE9UUEVtYWlsKGVtYWlsOiBzdHJpbmcsIG90cDogc3RyaW5nLCBmaXJzdE5hbWU/OiBzdHJpbmcsIHB1cnBvc2U6IHN0cmluZyA9ICdlbWFpbF92ZXJpZmljYXRpb24nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgY29uc3QgdGVtcGxhdGVOYW1lID0gcHVycG9zZSA9PT0gJ3Bhc3N3b3JkX3Jlc2V0JyA/ICdwYXNzd29yZF9yZXNldF9vdHAnIDogJ290cF92ZXJpZmljYXRpb24nO1xuICAgIGNvbnN0IHRlbXBsYXRlID0gYXdhaXQgdGhpcy5nZXRFbWFpbFRlbXBsYXRlKHRlbXBsYXRlTmFtZSk7XG5cbiAgICBpZiAoIXRlbXBsYXRlKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBFbWFpbCB0ZW1wbGF0ZSAnJHt0ZW1wbGF0ZU5hbWV9JyBub3QgZm91bmQuIFBsZWFzZSBlbnN1cmUgZW1haWwgdGVtcGxhdGVzIGFyZSBzZWVkZWQuYCk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgLy8gVXNlIGN1c3RvbSB0ZW1wbGF0ZVxuICAgIGxldCBodG1sID0gdGVtcGxhdGUuaHRtbENvbnRlbnQ7XG4gICAgbGV0IHRleHQgPSB0ZW1wbGF0ZS50ZXh0Q29udGVudCB8fCAnJztcbiAgICBcbiAgICAvLyBSZXBsYWNlIHBsYWNlaG9sZGVyc1xuICAgIGh0bWwgPSBodG1sLnJlcGxhY2UoL3t7Zmlyc3ROYW1lfX0vZywgZmlyc3ROYW1lIHx8ICdVc2VyJyk7XG4gICAgaHRtbCA9IGh0bWwucmVwbGFjZSgve3tvdHB9fS9nLCBvdHApO1xuICAgIHRleHQgPSB0ZXh0LnJlcGxhY2UoL3t7Zmlyc3ROYW1lfX0vZywgZmlyc3ROYW1lIHx8ICdVc2VyJyk7XG4gICAgdGV4dCA9IHRleHQucmVwbGFjZSgve3tvdHB9fS9nLCBvdHApO1xuXG4gICAgcmV0dXJuIGF3YWl0IHRoaXMuc2VuZEVtYWlsKHtcbiAgICAgIHRvOiBlbWFpbCxcbiAgICAgIHN1YmplY3Q6IHRlbXBsYXRlLnN1YmplY3QsXG4gICAgICBodG1sLFxuICAgICAgdGV4dCxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIGdldEVtYWlsVGVtcGxhdGUodGVtcGxhdGVOYW1lOiBzdHJpbmcpOiBQcm9taXNlPEVtYWlsVGVtcGxhdGUgfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRlbXBsYXRlID0gYXdhaXQgc3lzdGVtU2V0dGluZ3NEYi5nZXRFbWFpbFRlbXBsYXRlKHRlbXBsYXRlTmFtZSk7XG4gICAgICByZXR1cm4gdGVtcGxhdGU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZXQgZW1haWwgdGVtcGxhdGU6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgdGVzdENvbm5lY3Rpb24oKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCBmcmVzaCBjb25maWdcbiAgICAgIHRoaXMuY29uZmlnID0gYXdhaXQgdGhpcy5nZXRFbWFpbENvbmZpZygpO1xuICAgICAgaWYgKCF0aGlzLmNvbmZpZykge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFbWFpbCBjb25maWd1cmF0aW9uIG5vdCBmb3VuZCBvciBpbmNvbXBsZXRlJyk7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIHRyYW5zcG9ydGVyXG4gICAgICB0aGlzLnRyYW5zcG9ydGVyID0gbm9kZW1haWxlci5jcmVhdGVUcmFuc3BvcnQoe1xuICAgICAgICBob3N0OiB0aGlzLmNvbmZpZy5ob3N0LFxuICAgICAgICBwb3J0OiB0aGlzLmNvbmZpZy5wb3J0LFxuICAgICAgICBzZWN1cmU6IHRoaXMuY29uZmlnLnNlY3VyZSxcbiAgICAgICAgYXV0aDoge1xuICAgICAgICAgIHVzZXI6IHRoaXMuY29uZmlnLnVzZXIsXG4gICAgICAgICAgcGFzczogdGhpcy5jb25maWcucGFzc3dvcmQsXG4gICAgICAgIH0sXG4gICAgICAgIHRsczoge1xuICAgICAgICAgIHJlamVjdFVuYXV0aG9yaXplZDogZmFsc2UsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgLy8gVGVzdCBjb25uZWN0aW9uXG4gICAgICBhd2FpdCB0aGlzLnRyYW5zcG9ydGVyLnZlcmlmeSgpO1xuICAgICAgY29uc29sZS5sb2coJ0VtYWlsIGNvbm5lY3Rpb24gdGVzdCBzdWNjZXNzZnVsJyk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRW1haWwgY29ubmVjdGlvbiB0ZXN0IGZhaWxlZDonLCBlcnJvcik7XG4gICAgICB0aGlzLnRyYW5zcG9ydGVyID0gbnVsbDtcbiAgICAgIHRocm93IGVycm9yOyAvLyBSZS10aHJvdyB0byBnZXQgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZVxuICAgIH1cbiAgfVxufVxuXG4vLyBFeHBvcnQgc2luZ2xldG9uIGluc3RhbmNlXG5leHBvcnQgY29uc3QgZW1haWxTZXJ2aWNlID0gbmV3IEVtYWlsU2VydmljZSgpO1xuXG4vLyBVdGlsaXR5IGZ1bmN0aW9uc1xuZXhwb3J0IGNvbnN0IGdlbmVyYXRlT1RQID0gKCk6IHN0cmluZyA9PiB7XG4gIHJldHVybiBNYXRoLmZsb29yKDEwMDAwMCArIE1hdGgucmFuZG9tKCkgKiA5MDAwMDApLnRvU3RyaW5nKCk7XG59O1xuXG5leHBvcnQgY29uc3QgaXNWYWxpZEVtYWlsID0gKGVtYWlsOiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgY29uc3QgZW1haWxSZWdleCA9IC9eW15cXHNAXStAW15cXHNAXStcXC5bXlxcc0BdKyQvO1xuICByZXR1cm4gZW1haWxSZWdleC50ZXN0KGVtYWlsKTtcbn07XG4iXSwibmFtZXMiOlsibm9kZW1haWxlciIsInN5c3RlbVNldHRpbmdzRGIiLCJFbWFpbFNlcnZpY2UiLCJnZXRFbWFpbENvbmZpZyIsInNldHRpbmdzIiwiZ2V0RW1haWxTZXR0aW5ncyIsInNtdHBIb3N0Iiwic210cFVzZXIiLCJzbXRwUGFzc3dvcmQiLCJjb25zb2xlIiwid2FybiIsImhvc3QiLCJwb3J0Iiwic210cFBvcnQiLCJzZWN1cmUiLCJzbXRwU2VjdXJlIiwidXNlciIsInBhc3N3b3JkIiwiZnJvbU5hbWUiLCJmcm9tRW1haWwiLCJlcnJvciIsImluaXRpYWxpemVUcmFuc3BvcnRlciIsImNvbmZpZyIsInRyYW5zcG9ydGVyIiwiY3JlYXRlVHJhbnNwb3J0IiwiYXV0aCIsInBhc3MiLCJ0bHMiLCJyZWplY3RVbmF1dGhvcml6ZWQiLCJjb25uZWN0aW9uVGltZW91dCIsImdyZWV0aW5nVGltZW91dCIsInNvY2tldFRpbWVvdXQiLCJ2ZXJpZnlQcm9taXNlIiwidmVyaWZ5IiwidGltZW91dFByb21pc2UiLCJQcm9taXNlIiwiXyIsInJlamVjdCIsInNldFRpbWVvdXQiLCJFcnJvciIsInJhY2UiLCJsb2ciLCJzZW5kRW1haWwiLCJlbWFpbERhdGEiLCJpbml0aWFsaXplZCIsInRvIiwic3ViamVjdCIsIm1haWxPcHRpb25zIiwiZnJvbSIsImh0bWwiLCJ0ZXh0Iiwic2VuZFByb21pc2UiLCJzZW5kTWFpbCIsInJlc3VsdCIsIm1lc3NhZ2VJZCIsInNlbmRPVFBFbWFpbCIsImVtYWlsIiwib3RwIiwiZmlyc3ROYW1lIiwicHVycG9zZSIsInRlbXBsYXRlTmFtZSIsInRlbXBsYXRlIiwiZ2V0RW1haWxUZW1wbGF0ZSIsImh0bWxDb250ZW50IiwidGV4dENvbnRlbnQiLCJyZXBsYWNlIiwidGVzdENvbm5lY3Rpb24iLCJlbWFpbFNlcnZpY2UiLCJnZW5lcmF0ZU9UUCIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsInRvU3RyaW5nIiwiaXNWYWxpZEVtYWlsIiwiZW1haWxSZWdleCIsInRlc3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/errorLogger.ts":
/*!********************************!*\
  !*** ./src/lib/errorLogger.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorLogger: () => (/* binding */ ErrorLogger),\n/* harmony export */   setupGlobalErrorHandlers: () => (/* binding */ setupGlobalErrorHandlers),\n/* harmony export */   withErrorLogging: () => (/* binding */ withErrorLogging)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\nclass ErrorLogger {\n    /**\n   * Log server-side errors with comprehensive details\n   */ static async logError(data) {\n        try {\n            const error = data.error instanceof Error ? data.error : new Error(String(data.error));\n            const logData = {\n                action: data.action,\n                userId: data.userId,\n                adminId: data.adminId,\n                details: {\n                    message: error.message,\n                    stack: error.stack,\n                    name: error.name,\n                    requestUrl: data.requestUrl,\n                    requestMethod: data.requestMethod,\n                    requestBody: data.requestBody,\n                    additionalData: data.additionalData,\n                    timestamp: new Date().toISOString()\n                },\n                ipAddress: data.ipAddress,\n                userAgent: data.userAgent\n            };\n            await _database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create(logData);\n            // Also log to console for development\n            console.error(`[${data.action}] Error logged:`, {\n                message: error.message,\n                stack: error.stack,\n                userId: data.userId,\n                adminId: data.adminId,\n                url: data.requestUrl\n            });\n        } catch (logError) {\n            // Fallback to console if database logging fails\n            console.error('Failed to log error to database:', logError);\n            console.error('Original error:', data.error);\n        }\n    }\n    /**\n   * Log API route errors with request context\n   */ static async logApiError(request, error, action, userId, adminId, additionalData) {\n        try {\n            let requestBody = null;\n            // Safely extract request body\n            try {\n                if (request.method !== 'GET' && request.headers.get('content-type')?.includes('application/json')) {\n                    const clonedRequest = request.clone();\n                    requestBody = await clonedRequest.json();\n                    // Remove sensitive data from logs\n                    if (requestBody.password) requestBody.password = '[REDACTED]';\n                    if (requestBody.token) requestBody.token = '[REDACTED]';\n                    if (requestBody.apiKey) requestBody.apiKey = '[REDACTED]';\n                }\n            } catch (bodyError) {\n            // Ignore body parsing errors\n            }\n            await this.logError({\n                action,\n                error,\n                userId,\n                adminId,\n                ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n                userAgent: request.headers.get('user-agent') || 'unknown',\n                requestUrl: request.url,\n                requestMethod: request.method,\n                requestBody,\n                additionalData\n            });\n        } catch (logError) {\n            console.error('Failed to log API error:', logError);\n            console.error('Original error:', error);\n        }\n    }\n    /**\n   * Log client-side errors received from frontend\n   */ static async logClientError(data) {\n        try {\n            await _database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'CLIENT_ERROR',\n                userId: data.userId,\n                details: {\n                    message: data.message,\n                    stack: data.stack,\n                    url: data.url,\n                    timestamp: data.timestamp,\n                    additionalData: data.additionalData\n                },\n                userAgent: data.userAgent\n            });\n        } catch (logError) {\n            console.error('Failed to log client error:', logError);\n        }\n    }\n    /**\n   * Log authentication errors\n   */ static async logAuthError(request, error, email, additionalData) {\n        await this.logApiError(request, error, 'AUTH_ERROR', undefined, undefined, {\n            email,\n            ...additionalData\n        });\n    }\n    /**\n   * Log database errors\n   */ static async logDatabaseError(error, operation, table, userId, additionalData) {\n        try {\n            await _database__WEBPACK_IMPORTED_MODULE_0__.systemLogDb.create({\n                action: 'DATABASE_ERROR',\n                userId,\n                details: {\n                    message: error instanceof Error ? error.message : String(error),\n                    stack: error instanceof Error ? error.stack : undefined,\n                    operation,\n                    table,\n                    timestamp: new Date().toISOString(),\n                    additionalData\n                }\n            });\n        } catch (logError) {\n            console.error('Failed to log database error:', logError);\n            console.error('Original error:', error);\n        }\n    }\n    /**\n   * Log business logic errors\n   */ static async logBusinessError(error, operation, userId, adminId, additionalData) {\n        await this.logError({\n            action: 'BUSINESS_LOGIC_ERROR',\n            error,\n            userId,\n            adminId,\n            additionalData: {\n                operation,\n                ...additionalData\n            }\n        });\n    }\n    /**\n   * Log external API errors (Tron, payment gateways, etc.)\n   */ static async logExternalApiError(error, service, endpoint, userId, additionalData) {\n        await this.logError({\n            action: 'EXTERNAL_API_ERROR',\n            error,\n            userId,\n            additionalData: {\n                service,\n                endpoint,\n                ...additionalData\n            }\n        });\n    }\n    /**\n   * Log validation errors\n   */ static async logValidationError(error, field, value, userId, additionalData) {\n        await this.logError({\n            action: 'VALIDATION_ERROR',\n            error,\n            userId,\n            additionalData: {\n                field,\n                value,\n                ...additionalData\n            }\n        });\n    }\n}\n/**\n * Middleware function to wrap API routes with error logging\n */ function withErrorLogging(handler, actionName) {\n    return async (...args)=>{\n        try {\n            return await handler(...args);\n        } catch (error) {\n            // Extract request if it's the first argument\n            const request = args[0];\n            if (request && typeof request === 'object' && 'url' in request) {\n                await ErrorLogger.logApiError(request, error, actionName);\n            } else {\n                await ErrorLogger.logError({\n                    action: actionName,\n                    error: error\n                });\n            }\n            throw error; // Re-throw to maintain original behavior\n        }\n    };\n}\n/**\n * Global error handler for unhandled promise rejections\n */ function setupGlobalErrorHandlers() {\n    if (typeof process !== 'undefined') {\n        process.on('unhandledRejection', async (reason, promise)=>{\n            console.error('Unhandled Rejection at:', promise, 'reason:', reason);\n            await ErrorLogger.logError({\n                action: 'UNHANDLED_REJECTION',\n                error: reason instanceof Error ? reason : new Error(String(reason)),\n                additionalData: {\n                    promise: promise.toString()\n                }\n            });\n        });\n        process.on('uncaughtException', async (error)=>{\n            console.error('Uncaught Exception:', error);\n            await ErrorLogger.logError({\n                action: 'UNCAUGHT_EXCEPTION',\n                error\n            });\n            // Exit process after logging\n            process.exit(1);\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/errorLogger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZHJlYW1cXERlc2t0b3BcXEhhc2hfTWluaW5nc1xcaGFzaGNvcmV4XFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/nodemailer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fsend-otp%2Froute&page=%2Fapi%2Fauth%2Fsend-otp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fsend-otp%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5CHash_Minings%5Chashcorex&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();